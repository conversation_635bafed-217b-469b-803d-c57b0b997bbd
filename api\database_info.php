<?php
/**
 * API endpoint pro získání informací o databázi
 */

header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Načtení konfigurace databáze
    $config = require_once __DIR__ . '/../config/database.php';
    
    // Test připojení k databázi
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    
    // Získání informací o databázi
    $stmt = $pdo->query("SELECT DATABASE() as current_database");
    $dbInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Získání verze MySQL
    $stmt = $pdo->query("SELECT VERSION() as mysql_version");
    $versionInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Kontrola základních tabulek
    $requiredTables = ['products', 'stockcurrent', 'people'];
    $existingTables = [];
    $missingTables = [];
    
    foreach ($requiredTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $existingTables[] = $table;
        } else {
            $missingTables[] = $table;
        }
    }
    
    // Kontrola inventurních tabulek
    $inventoryTables = ['inventory_sessions', 'inventory_entries', 'inventory_users', 'inventory_totals'];
    $existingInventoryTables = [];
    $missingInventoryTables = [];
    
    foreach ($inventoryTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $existingInventoryTables[] = $table;
        } else {
            $missingInventoryTables[] = $table;
        }
    }
    
    // Počet produktů
    $productCount = 0;
    if (in_array('products', $existingTables)) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM products");
        $productCount = $stmt->fetchColumn();
    }
    
    // Počet uživatelů
    $userCount = 0;
    if (in_array('inventory_users', $existingInventoryTables)) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM inventory_users");
        $userCount = $stmt->fetchColumn();
    }
    
    // Stav připojení
    $connectionStatus = 'connected';
    $statusMessage = 'Připojení k databázi je aktivní';
    
    if (!empty($missingTables)) {
        $connectionStatus = 'warning';
        $statusMessage = 'Připojení funguje, ale chybí některé základní tabulky';
    }
    
    if (!empty($missingInventoryTables)) {
        $connectionStatus = 'warning';
        $statusMessage = 'Připojení funguje, ale chybí některé inventurní tabulky';
    }
    
    // Odpověď
    echo json_encode([
        'success' => true,
        'connection_status' => $connectionStatus,
        'status_message' => $statusMessage,
        'database_info' => [
            'host' => $config['host'],
            'database' => $config['dbname'],
            'username' => $config['username'],
            'current_database' => $dbInfo['current_database'],
            'mysql_version' => $versionInfo['mysql_version']
        ],
        'tables' => [
            'required_tables' => [
                'existing' => $existingTables,
                'missing' => $missingTables,
                'total' => count($requiredTables),
                'existing_count' => count($existingTables)
            ],
            'inventory_tables' => [
                'existing' => $existingInventoryTables,
                'missing' => $missingInventoryTables,
                'total' => count($inventoryTables),
                'existing_count' => count($existingInventoryTables)
            ]
        ],
        'statistics' => [
            'product_count' => $productCount,
            'user_count' => $userCount
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // Chyba při připojení
    echo json_encode([
        'success' => false,
        'connection_status' => 'error',
        'status_message' => 'Chyba při připojení k databázi',
        'error' => $e->getMessage(),
        'database_info' => [
            'host' => $config['host'] ?? 'neznámý',
            'database' => $config['dbname'] ?? 'neznámá',
            'username' => $config['username'] ?? 'neznámý'
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?>
