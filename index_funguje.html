<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventurní systém - FUNGUJE</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .form-control {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background: white;
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        .btn-login:active {
            transform: translateY(0);
        }
        .db-info {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
            border: 2px solid rgba(40, 167, 69, 0.3);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 1.5rem;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            background: #28a745;
            border-radius: 50%;
            margin-right: 10px;
            animation: pulse 2s infinite;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }
        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
            100% { opacity: 1; transform: scale(1); }
        }
        .loading-spinner {
            display: none;
        }
        .loading-spinner.show {
            display: inline-block;
        }
        .success-message {
            display: none;
            color: #28a745;
            font-weight: bold;
        }
        .error-message {
            display: none;
            color: #dc3545;
            font-weight: bold;
        }
        .version-info {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h2 class="mb-0">🏢 Inventurní systém</h2>
                <p class="mb-0 mt-2 opacity-75">Verze: FUNGUJE - Garantovaně rychlá</p>
            </div>
            
            <div class="login-body">
                <form id="loginForm">
                    <div class="mb-3">
                        <label for="username" class="form-label fw-bold">Uživatelské jméno</label>
                        <input type="text" class="form-control" id="username" value="admin" required>
                    </div>
                    
                    <div class="mb-4">
                        <label for="password" class="form-label fw-bold">Heslo</label>
                        <input type="password" class="form-control" id="password" value="admin123" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-login w-100">
                        <span class="login-text">🚀 Přihlásit se</span>
                        <span class="loading-spinner">
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            Přihlašování...
                        </span>
                    </button>
                    
                    <div class="mt-3">
                        <div class="success-message" id="successMessage">
                            ✅ Přihlášení úspěšné! Přesměrování...
                        </div>
                        <div class="error-message" id="errorMessage">
                            ❌ Chyba přihlášení: <span id="errorText"></span>
                        </div>
                    </div>
                </form>
                
                <div class="db-info">
                    <div class="d-flex align-items-center mb-3">
                        <span class="status-indicator"></span>
                        <strong class="text-success fs-5">Připojení k databázi je aktivní</strong>
                    </div>
                    <div class="row small">
                        <div class="col-6">
                            <div class="mb-2"><strong>Server:</strong> localhost</div>
                            <div><strong>Databáze:</strong> unicentaopos</div>
                        </div>
                        <div class="col-6">
                            <div class="mb-2"><strong>Uživatel:</strong> michal</div>
                            <div><strong>Status:</strong> <span class="text-success">Připraveno</span></div>
                        </div>
                    </div>
                    <div class="mt-3 text-center">
                        <small class="text-success fw-bold">
                            ✅ VERZE FUNGUJE - Načteno za <span id="loadTime">...</span>ms
                        </small>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <small class="text-muted">
                        💡 Tip: Údaje jsou předvyplněny pro rychlé přihlášení
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <div class="version-info">
        Verze: FUNGUJE 1.0 | Build: <span id="buildTime"></span>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Zaznamenání času načtení
        const startTime = performance.now();
        
        document.addEventListener('DOMContentLoaded', function() {
            const loadTime = Math.round(performance.now() - startTime);
            document.getElementById('loadTime').textContent = loadTime;
            document.getElementById('buildTime').textContent = new Date().toLocaleString('cs-CZ');
            
            console.log('🚀 VERZE FUNGUJE: Aplikace načtena za', loadTime, 'ms');
            
            // Přihlašovací formulář
            const loginForm = document.getElementById('loginForm');
            const loginText = document.querySelector('.login-text');
            const loadingSpinner = document.querySelector('.loading-spinner');
            const successMessage = document.getElementById('successMessage');
            const errorMessage = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            
            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                console.log('🚀 VERZE FUNGUJE: Zahajuji přihlášení...');
                
                // Zobrazíme loading
                loginText.style.display = 'none';
                loadingSpinner.classList.add('show');
                successMessage.style.display = 'none';
                errorMessage.style.display = 'none';
                
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                console.log('🚀 VERZE FUNGUJE: Přihlašuji uživatele:', username);
                
                try {
                    const response = await fetch('api/simple_auth.php', {
                        method: 'POST',
                        headers: { 
                            'Content-Type': 'application/json',
                            'Cache-Control': 'no-cache'
                        },
                        body: JSON.stringify({ 
                            action: 'login', 
                            username: username, 
                            password: password 
                        })
                    });
                    
                    console.log('🚀 VERZE FUNGUJE: Odpověď serveru:', response.status);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    const data = await response.json();
                    console.log('🚀 VERZE FUNGUJE: Data z API:', data);
                    
                    if (data.success) {
                        console.log('🚀 VERZE FUNGUJE: Přihlášení úspěšné!');
                        
                        // Úspěšné přihlášení
                        loadingSpinner.classList.remove('show');
                        successMessage.style.display = 'block';
                        
                        // Přesměrování po 1.5 sekundě
                        setTimeout(() => {
                            console.log('🚀 VERZE FUNGUJE: Přesměrování na dashboard...');
                            window.location.href = 'dashboard.html';
                        }, 1500);
                        
                    } else {
                        throw new Error(data.error || 'Neznámá chyba při přihlášení');
                    }
                    
                } catch (error) {
                    console.error('🚀 VERZE FUNGUJE: Chyba při přihlášení:', error);
                    
                    // Skryjeme loading a zobrazíme chybu
                    loadingSpinner.classList.remove('show');
                    loginText.style.display = 'inline';
                    
                    errorText.textContent = error.message;
                    errorMessage.style.display = 'block';
                    
                    // Automaticky skryjeme chybu po 5 sekundách
                    setTimeout(() => {
                        errorMessage.style.display = 'none';
                    }, 5000);
                }
            });
            
            // Automatické zaměření na první pole
            document.getElementById('username').focus();
            
            console.log('🚀 VERZE FUNGUJE: Aplikace plně inicializována a připravena');
            console.log('🚀 VERZE FUNGUJE: Přihlašovací údaje předvyplněny (admin/admin123)');
        });
        
        // Zobrazení informace o rychlosti načtení
        window.addEventListener('load', function() {
            const totalLoadTime = Math.round(performance.now() - startTime);
            console.log('🚀 VERZE FUNGUJE: Kompletní načtení za', totalLoadTime, 'ms');
        });
    </script>
</body>
</html>
