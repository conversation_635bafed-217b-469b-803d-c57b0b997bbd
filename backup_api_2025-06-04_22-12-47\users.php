<?php
/**
 * Uživatelé API
 *
 * Tento soubor zpracovává API endpointy související se správou uživatelů.
 */

require_once __DIR__ . '/../utils/database.php';
require_once __DIR__ . '/../utils/auth.php';
require_once __DIR__ . '/../utils/validation.php';

// Zapnutí zobrazování chyb pro ladění
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Nastavení typu obsahu na JSON
header('Content-Type: application/json');

// Ladící výpisy
error_log("REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
error_log("REQUEST_URI: " . $_SERVER['REQUEST_URI']);
error_log("QUERY_STRING: " . $_SERVER['QUERY_STRING']);
error_log("GET params: " . print_r($_GET, true));
error_log("POST params: " . print_r($_POST, true));
error_log("JSON input: " . file_get_contents('php://input'));

// Kontrola a vytvoření potřebných tabulek a výchozího administrátorského účtu
try {
    ensureTablesExist();
} catch (PDOException $e) {
    // Pokud se nepodaří vytvořit tabulky, vrátíme chybu
    sendResponse([
        'error' => 'Nepodařilo se vytvořit potřebné tabulky',
        'message' => $e->getMessage()
    ], 500);
    exit;
}


// Získání metody požadavku
$method = $_SERVER['REQUEST_METHOD'];

// Získání cesty požadavku
$path = $_SERVER['PATH_INFO'] ?? '/';

// Dočasně vypnuto pro testování
// Kontrola, zda je uživatel přihlášen
// if (!isLoggedIn()) {
//     sendResponse(['error' => 'Neautorizovaný přístup'], 401);
//     exit;
// }

// Získání akce z query stringu (podpora obou 'action' a 'endpoint' pro zpětnou kompatibilitu)
$action = $_GET['action'] ?? $_GET['endpoint'] ?? '';

// Kontrola, zda cesta obsahuje ID uživatele (např. /users/15)
$pathParts = explode('/', trim($path, '/'));
if (count($pathParts) > 0 && is_numeric(end($pathParts))) {
    // Extrahujeme ID uživatele z cesty
    $userId = intval(end($pathParts));
    $_GET['id'] = $userId;

    // Upravíme cestu tak, aby neobsahovala ID
    $path = '/';

    error_log("Extrahováno ID uživatele z cesty: " . $userId);
}

// Zpracování různých endpointů
if (empty($path) || $path === '/') {
    // Hlavní endpoint /api/users
    if ($method === 'GET') {
        if (isset($_GET['id'])) {
            // Pokud je zadáno ID, vrátíme detail uživatele
            handleUserDetail();
        } else {
            // Jinak vrátíme seznam uživatelů
            handleUsersList();
        }
    } else if ($method === 'POST') {
        if ($action === 'create') {
            // Vytvoření nového uživatele
            handleUserCreate();
        } else if ($action === 'update') {
            // Aktualizace uživatele
            handleUserUpdate();
        } else if ($action === 'delete') {
            // Smazání uživatele
            handleUserDelete();
        } else if ($action === 'password') {
            // Změna hesla
            handlePasswordChange();
        } else {
            sendResponse(['error' => 'Neplatná akce'], 400);
        }
    } else if ($method === 'DELETE') {
        if ($action === 'delete' && isset($_GET['id'])) {
            // Smazání uživatele
            handleUserDelete();
        } else {
            sendResponse(['error' => 'Neplatná akce nebo chybí ID'], 400);
        }
    } else {
        sendResponse(['error' => 'Metoda není povolena'], 405);
    }
} else {
    // Zpracování starých endpointů pro zpětnou kompatibilitu
    switch ($path) {
        case '/list':
            handleUsersList();
            break;

        case '/detail':
            handleUserDetail();
            break;

        case '/create':
            handleUserCreate();
            break;

        case '/update':
            handleUserUpdate();
            break;

        case '/delete':
            handleUserDelete();
            break;

        case '/password':
            handlePasswordChange();
            break;

        default:
            sendResponse(['error' => 'Endpoint nenalezen: ' . $path], 404);
            break;
    }
}

/**
 * Zpracování požadavku na seznam uživatelů
 */
function handleUsersList() {
    global $method;

    if ($method !== 'GET') {
        sendResponse(['error' => 'Metoda není povolena'], 405);
        return;
    }

    // Kontrola, zda je uživatel admin nebo manager
    if (!isAdminOrManager()) {
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    try {
        $pdo = getDbConnection();

        $stmt = $pdo->prepare("
            SELECT
                id,
                username,
                role,
                full_name,
                email,
                active,
                created_at,
                updated_at
            FROM
                inventory_users
            ORDER BY
                username
        ");

        $stmt->execute();

        $users = $stmt->fetchAll();

        sendResponse(['users' => $users]);
    } catch (Exception $e) {
        error_log("Chyba při načítání uživatelů: " . $e->getMessage());
        sendResponse([
            'error' => 'Došlo k chybě při načítání uživatelů',
            'message' => $e->getMessage(),
            'debug_info' => [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]
        ], 500);
    }
}

/**
 * Zpracování požadavku na detail uživatele
 */
function handleUserDetail() {
    global $method;

    if ($method !== 'GET') {
        sendResponse(['error' => 'Metoda není povolena'], 405);
        return;
    }

    // Získání ID uživatele
    $userId = $_GET['id'] ?? null;

    if (!$userId) {
        // Pokud není zadáno ID, vrátíme seznam uživatelů
        handleUsersList();
        return;
    }

    // Validace ID uživatele
    $userId = validateInt($userId);

    if (!$userId) {
        sendResponse(['error' => 'Neplatné ID uživatele'], 400);
        return;
    }

    // Kontrola oprávnění - běžný uživatel může vidět pouze svůj profil
    $currentUser = getCurrentUser();

    if (!isAdminOrManager() && $userId != $currentUser['id']) {
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    $pdo = getDbConnection();

    $stmt = $pdo->prepare("
        SELECT
            id,
            username,
            role,
            full_name,
            email,
            active,
            created_at,
            updated_at
        FROM
            inventory_users
        WHERE
            id = :id
    ");

    $stmt->execute(['id' => $userId]);

    $user = $stmt->fetch();

    if (!$user) {
        sendResponse(['error' => 'Uživatel nenalezen'], 404);
        return;
    }

    sendResponse(['user' => $user]);
}

/**
 * Zpracování požadavku na vytvoření uživatele
 */
function handleUserCreate() {
    global $method;

    if ($method !== 'POST') {
        sendResponse(['error' => 'Metoda není povolena'], 405);
        return;
    }

    // Kontrola, zda je uživatel admin nebo manager
    if (!isAdminOrManager()) {
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    // Získání JSON vstupu
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        sendResponse(['error' => 'Neplatný JSON'], 400);
        return;
    }

    // Ladící výpisy
    error_log("handleUserCreate - input: " . print_r($input, true));

    // Dočasně vypneme validaci a použijeme přímé hodnoty z vstupu
    if (empty($input['username'])) {
        sendResponse(['error' => 'Uživatelské jméno je povinné'], 400);
        return;
    }

    if (empty($input['password'])) {
        sendResponse(['error' => 'Heslo je povinné'], 400);
        return;
    }

    if (empty($input['role'])) {
        sendResponse(['error' => 'Role je povinná'], 400);
        return;
    }

    if (empty($input['full_name'])) {
        sendResponse(['error' => 'Celé jméno je povinné'], 400);
        return;
    }

    // Použijeme přímé hodnoty z vstupu
    $username = $input['username'];
    $password = $input['password'];
    $role = $input['role'];
    $fullName = $input['full_name'];
    $email = $input['email'] ?? null;
    $active = $input['active'] ?? 1;

    // Přeskočíme validaci a pokračujeme přímo k vytvoření uživatele
    error_log("handleUserCreate - použité hodnoty: username=$username, role=$role, fullName=$fullName, email=$email, active=$active");

    // Kontrola, zda je role platná
    $validRoles = ['admin', 'manager', 'user'];

    if (!in_array($role, $validRoles)) {
        sendResponse(['error' => 'Neplatná role'], 400);
        return;
    }

    $pdo = getDbConnection();

    // Kontrola, zda uživatelské jméno již existuje
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM inventory_users WHERE username = :username");
    $stmt->execute(['username' => $username]);

    if ($stmt->fetchColumn() > 0) {
        sendResponse(['error' => 'Uživatelské jméno již existuje'], 400);
        return;
    }

    try {
        // Vytvoření uživatele
        $stmt = $pdo->prepare("
            INSERT INTO inventory_users (
                username,
                password,
                role,
                full_name,
                email,
                active
            )
            VALUES (
                :username,
                :password,
                :role,
                :full_name,
                :email,
                :active
            )
        ");

        $stmt->execute([
            'username' => $username,
            'password' => password_hash($password, PASSWORD_DEFAULT),
            'role' => $role,
            'full_name' => $fullName,
            'email' => $email,
            'active' => $active
        ]);

        $userId = $pdo->lastInsertId();

        sendResponse([
            'success' => true,
            'user_id' => $userId,
            'message' => 'Uživatel byl úspěšně vytvořen'
        ]);
    } catch (Exception $e) {
        error_log("Chyba při vytváření uživatele: " . $e->getMessage());
        sendResponse(['error' => 'Došlo k chybě při vytváření uživatele'], 500);
    }
}

/**
 * Zpracování požadavku na aktualizaci uživatele
 */
function handleUserUpdate() {
    global $method;

    // Akceptujeme jak PUT, tak POST s action=update
    if ($method !== 'PUT' && $method !== 'POST') {
        sendResponse(['error' => 'Metoda není povolena'], 405);
        return;
    }

    // Získání JSON vstupu
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        sendResponse(['error' => 'Neplatný JSON'], 400);
        return;
    }

    // Získání ID uživatele z query stringu nebo z JSON vstupu
    $userId = $_GET['id'] ?? ($input['id'] ?? null);

    if (!$userId) {
        sendResponse(['error' => 'ID uživatele je povinné'], 400);
        return;
    }

    // Validace ID uživatele
    $userId = validateInt($userId);

    if (!$userId) {
        sendResponse(['error' => 'Neplatné ID uživatele'], 400);
        return;
    }

    // Validace vstupu
    $schema = [
        'username' => ['type' => 'string', 'required' => false],
        'password' => ['type' => 'string', 'required' => false],
        'role' => ['type' => 'string', 'required' => false],
        'full_name' => ['type' => 'string', 'required' => false],
        'email' => ['type' => 'email', 'required' => false],
        'active' => ['type' => 'int', 'required' => false]
    ];

    $validation = validateData($input, $schema);

    if (!$validation['valid']) {
        sendResponse(['error' => 'Validace selhala', 'details' => $validation['errors']], 400);
        return;
    }

    // Kontrola oprávnění - běžný uživatel může upravovat pouze svůj profil a nemůže měnit roli
    $currentUser = getCurrentUser();

    if (!isAdminOrManager() && $userId != $currentUser['id']) {
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    // Běžný uživatel nemůže měnit roli ani aktivní stav
    if (!isAdminOrManager() && (isset($input['role']) || isset($input['active']))) {
        sendResponse(['error' => 'Nedostatečná oprávnění pro změnu role nebo aktivního stavu'], 403);
        return;
    }

    $pdo = getDbConnection();

    // Kontrola, zda uživatel existuje
    $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE id = :id");
    $stmt->execute(['id' => $userId]);

    $user = $stmt->fetch();

    if (!$user) {
        sendResponse(['error' => 'Uživatel nenalezen'], 404);
        return;
    }

    // Příprava dat pro aktualizaci
    $updateFields = [];
    $params = ['id' => $userId];

    // Zpracování uživatelského jména
    if (isset($validation['data']['username'])) {
        $username = $validation['data']['username'];

        // Kontrola, zda uživatelské jméno již existuje u jiného uživatele
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM inventory_users WHERE username = :username AND id != :id");
        $stmt->execute(['username' => $username, 'id' => $userId]);

        if ($stmt->fetchColumn() > 0) {
            sendResponse(['error' => 'Uživatelské jméno již existuje'], 400);
            return;
        }

        $updateFields[] = "username = :username";
        $params['username'] = $username;
    }

    if (isset($validation['data']['role'])) {
        $role = $validation['data']['role'];

        // Kontrola, zda je role platná
        $validRoles = ['admin', 'manager', 'user'];

        if (!in_array($role, $validRoles)) {
            sendResponse(['error' => 'Neplatná role'], 400);
            return;
        }

        $updateFields[] = "role = :role";
        $params['role'] = $role;
    }

    if (isset($validation['data']['full_name'])) {
        $updateFields[] = "full_name = :full_name";
        $params['full_name'] = $validation['data']['full_name'];
    }

    if (isset($validation['data']['email'])) {
        $updateFields[] = "email = :email";
        $params['email'] = $validation['data']['email'];
    }

    if (isset($validation['data']['active'])) {
        $updateFields[] = "active = :active";
        $params['active'] = $validation['data']['active'];
    }

    // Zpracování hesla, pokud bylo zadáno
    if (isset($validation['data']['password']) && !empty($validation['data']['password'])) {
        $updateFields[] = "password = :password";
        $params['password'] = password_hash($validation['data']['password'], PASSWORD_DEFAULT);
    }

    if (empty($updateFields)) {
        sendResponse(['error' => 'Žádná data k aktualizaci'], 400);
        return;
    }

    try {
        // Aktualizace uživatele
        $sql = "UPDATE inventory_users SET " . implode(", ", $updateFields) . " WHERE id = :id";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        sendResponse([
            'success' => true,
            'message' => 'Uživatel byl úspěšně aktualizován'
        ]);
    } catch (Exception $e) {
        error_log("Chyba při aktualizaci uživatele: " . $e->getMessage());
        sendResponse(['error' => 'Došlo k chybě při aktualizaci uživatele'], 500);
    }
}

/**
 * Zpracování požadavku na smazání uživatele
 */
function handleUserDelete() {
    global $method;

    // Akceptujeme jak DELETE, tak POST s action=delete
    if ($method !== 'DELETE' && $method !== 'POST') {
        sendResponse(['error' => 'Metoda není povolena'], 405);
        return;
    }

    // Kontrola, zda je uživatel admin nebo manager
    if (!isAdminOrManager()) {
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    // Získání ID uživatele
    $userId = $_GET['id'] ?? null;

    if (!$userId) {
        sendResponse(['error' => 'ID uživatele je povinné'], 400);
        return;
    }

    // Validace ID uživatele
    $userId = validateInt($userId);

    if (!$userId) {
        sendResponse(['error' => 'Neplatné ID uživatele'], 400);
        return;
    }

    $pdo = getDbConnection();

    // Kontrola, zda uživatel existuje
    $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE id = :id");
    $stmt->execute(['id' => $userId]);

    $user = $stmt->fetch();

    if (!$user) {
        sendResponse(['error' => 'Uživatel nenalezen'], 404);
        return;
    }

    // Kontrola, zda uživatel není aktuálně přihlášený uživatel
    $currentUser = getCurrentUser();

    if ($userId == $currentUser['id']) {
        sendResponse(['error' => 'Nemůžete smazat sami sebe'], 400);
        return;
    }

    try {
        // Skutečné smazání uživatele z databáze
        $stmt = $pdo->prepare("DELETE FROM inventory_users WHERE id = :id");
        $stmt->execute(['id' => $userId]);

        sendResponse([
            'success' => true,
            'message' => 'Uživatel byl úspěšně smazán z databáze'
        ]);
    } catch (Exception $e) {
        error_log("Chyba při mazání uživatele: " . $e->getMessage());
        sendResponse(['error' => 'Došlo k chybě při mazání uživatele: ' . $e->getMessage()], 500);
    }
}

/**
 * Zpracování požadavku na změnu hesla
 */
function handlePasswordChange() {
    global $method;

    if ($method !== 'POST') {
        sendResponse(['error' => 'Metoda není povolena'], 405);
        return;
    }

    // Získání JSON vstupu
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        sendResponse(['error' => 'Neplatný JSON'], 400);
        return;
    }

    // Validace vstupu
    $schema = [
        'id' => ['type' => 'int', 'required' => true],
        'current_password' => ['type' => 'password', 'required' => false],
        'new_password' => ['type' => 'password', 'required' => true, 'minLength' => 8]
    ];

    $validation = validateData($input, $schema);

    if (!$validation['valid']) {
        sendResponse(['error' => 'Validace selhala', 'details' => $validation['errors']], 400);
        return;
    }

    $userId = $validation['data']['id'];
    $currentPassword = $validation['data']['current_password'] ?? null;
    $newPassword = $validation['data']['new_password'];

    // Kontrola oprávnění - běžný uživatel může měnit pouze své heslo a musí zadat aktuální heslo
    $currentUser = getCurrentUser();

    if (!isAdminOrManager() && $userId != $currentUser['id']) {
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    if (!isAdminOrManager() && $userId == $currentUser['id'] && !$currentPassword) {
        sendResponse(['error' => 'Aktuální heslo je povinné'], 400);
        return;
    }

    $pdo = getDbConnection();

    // Kontrola, zda uživatel existuje
    $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE id = :id");
    $stmt->execute(['id' => $userId]);

    $user = $stmt->fetch();

    if (!$user) {
        sendResponse(['error' => 'Uživatel nenalezen'], 404);
        return;
    }

    // Kontrola aktuálního hesla, pokud je vyžadováno
    if (!isAdminOrManager() && $userId == $currentUser['id'] && !password_verify($currentPassword, $user['password'])) {
        sendResponse(['error' => 'Nesprávné aktuální heslo'], 400);
        return;
    }

    try {
        // Aktualizace hesla
        $stmt = $pdo->prepare("UPDATE inventory_users SET password = :password WHERE id = :id");
        $stmt->execute([
            'id' => $userId,
            'password' => password_hash($newPassword, PASSWORD_DEFAULT)
        ]);

        sendResponse([
            'success' => true,
            'message' => 'Heslo bylo úspěšně změněno'
        ]);
    } catch (Exception $e) {
        error_log("Chyba při změně hesla: " . $e->getMessage());
        sendResponse(['error' => 'Došlo k chybě při změně hesla'], 500);
    }
}

/**
 * Odeslání JSON odpovědi
 *
 * @param mixed $data Data odpovědi
 * @param int $statusCode HTTP stavový kód
 */
function sendResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}
