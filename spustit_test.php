<?php
/**
 * <PERSON><PERSON><PERSON> spuštění testu aplikace
 */

echo "<h1>Spuštění testu inventurní aplikace</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .button { 
        display: inline-block; 
        padding: 10px 20px; 
        margin: 10px; 
        background: #007bff; 
        color: white; 
        text-decoration: none; 
        border-radius: 5px; 
        border: none;
        cursor: pointer;
    }
    .button:hover { background: #0056b3; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
</style>";

echo "<h2>Dostupné akce:</h2>";

echo "<div>";
echo "<a href='rychly_test.php' class='button' target='_blank'>⚡ Rychlý test</a>";
echo "<a href='test_aplikace.php' class='button' target='_blank'>🔍 Kompletní test</a>";
echo "<a href='test_api_produkty.php' class='button' target='_blank'>🔌 Test API produkty</a>";
echo "<a href='index.html' class='button' target='_blank'>🚀 Otevřít aplikaci</a>";
echo "</div>";

echo "<h3>🔧 Nástroje pro opravu:</h3>";
echo "<div>";
echo "<a href='check_tables.php' class='button' target='_blank'>🗄️ Zkontrolovat tabulky</a>";
echo "<a href='oprava_api_test.php' class='button' target='_blank'>🔧 Opravit API pro testování</a>";
echo "</div>";

echo "<h2>Rychlá kontrola stavu:</h2>";

// Kontrola připojení k databázi
try {
    $config = require_once 'config/database.php';
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    echo "<p class='success'>✓ Připojení k databázi funguje</p>";
    
    // Kontrola základních tabulek
    $tables = ['products', 'stockcurrent', 'people'];
    $missing = [];
    
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() == 0) {
            $missing[] = $table;
        }
    }
    
    if (empty($missing)) {
        echo "<p class='success'>✓ Základní tabulky UniCentaOPOS existují</p>";
    } else {
        echo "<p class='error'>✗ Chybí tabulky: " . implode(', ', $missing) . "</p>";
    }
    
    // Kontrola inventurních tabulek
    $inv_tables = ['inventory_sessions', 'inventory_entries', 'inventory_users'];
    $missing_inv = [];
    
    foreach ($inv_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() == 0) {
            $missing_inv[] = $table;
        }
    }
    
    if (empty($missing_inv)) {
        echo "<p class='success'>✓ Inventurní tabulky existují</p>";
    } else {
        echo "<p class='error'>✗ Chybí inventurní tabulky: " . implode(', ', $missing_inv) . "</p>";
        echo "<p class='info'>💡 Spusťte 'Zkontrolovat tabulky' pro jejich vytvoření</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>✗ Chyba při připojení k databázi: " . $e->getMessage() . "</p>";
}

echo "<h2>Přihlašovací údaje:</h2>";
echo "<p><strong>Výchozí administrátor:</strong></p>";
echo "<ul>";
echo "<li><strong>Uživatelské jméno:</strong> admin</li>";
echo "<li><strong>Heslo:</strong> admin123</li>";
echo "</ul>";

echo "<h2>Návod k testování:</h2>";
echo "<ol>";
echo "<li>Klikněte na 'Spustit kompletní test' pro automatickou kontrolu</li>";
echo "<li>Pokud test projde úspěšně, klikněte na 'Otevřít aplikaci'</li>";
echo "<li>Přihlaste se pomocí výchozích údajů</li>";
echo "<li>Postupujte podle testovacího plánu v souboru TESTOVACI_PLAN.md</li>";
echo "</ol>";

echo "<h2>Užitečné odkazy:</h2>";
echo "<ul>";
echo "<li><a href='TESTOVACI_PLAN.md' target='_blank'>📋 Testovací plán (Markdown)</a></li>";
echo "<li><a href='api/' target='_blank'>🔌 API dokumentace</a></li>";
echo "<li><a href='database/schema.sql' target='_blank'>🗄️ Databázové schéma</a></li>";
echo "</ul>";

echo "<hr>";
echo "<p><em>Vytvořeno: " . date('Y-m-d H:i:s') . "</em></p>";
?>
