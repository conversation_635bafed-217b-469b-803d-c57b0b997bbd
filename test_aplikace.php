<?php
/**
 * Test aplikace - Komplexní test inventurního systému
 */

echo "<h1>Test inventurní aplikace</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    .warning { color: orange; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>\n";

// Načtení konfigurace databáze
try {
    $config = require_once 'config/database.php';
    echo "<div class='test-section'>";
    echo "<h2>1. Konfigurace databáze</h2>";
    echo "<p class='success'>✓ Konfigurace databáze načtena úspěšně</p>";
    echo "<pre>" . print_r($config, true) . "</pre>";
    echo "</div>";
} catch (Exception $e) {
    echo "<div class='test-section'>";
    echo "<h2>1. Konfigurace databáze</h2>";
    echo "<p class='error'>✗ Chyba při načítání konfigurace: " . $e->getMessage() . "</p>";
    echo "</div>";
    exit;
}

// Test připojení k databázi
try {
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    
    echo "<div class='test-section'>";
    echo "<h2>2. Připojení k databázi</h2>";
    echo "<p class='success'>✓ Připojení k databázi úspěšné</p>";
    echo "<p><strong>Server:</strong> {$config['host']}</p>";
    echo "<p><strong>Databáze:</strong> {$config['dbname']}</p>";
    echo "<p><strong>Uživatel:</strong> {$config['username']}</p>";
    echo "</div>";
} catch (Exception $e) {
    echo "<div class='test-section'>";
    echo "<h2>2. Připojení k databázi</h2>";
    echo "<p class='error'>✗ Chyba při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "</div>";
    exit;
}

// Test existence základních tabulek UniCentaOPOS
echo "<div class='test-section'>";
echo "<h2>3. Kontrola základních tabulek UniCentaOPOS</h2>";

$required_tables = ['products', 'stockcurrent', 'people', 'roles', 'tickets', 'ticketlines'];
$existing_tables = [];
$missing_tables = [];

foreach ($required_tables as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $existing_tables[] = $table;
            echo "<p class='success'>✓ Tabulka '$table' existuje</p>";
        } else {
            $missing_tables[] = $table;
            echo "<p class='error'>✗ Tabulka '$table' neexistuje</p>";
        }
    } catch (Exception $e) {
        $missing_tables[] = $table;
        echo "<p class='error'>✗ Chyba při kontrole tabulky '$table': " . $e->getMessage() . "</p>";
    }
}

if (empty($missing_tables)) {
    echo "<p class='success'><strong>✓ Všechny základní tabulky UniCentaOPOS existují</strong></p>";
} else {
    echo "<p class='warning'><strong>⚠ Některé tabulky chybí: " . implode(', ', $missing_tables) . "</strong></p>";
}
echo "</div>";

// Test existence inventurních tabulek
echo "<div class='test-section'>";
echo "<h2>4. Kontrola inventurních tabulek</h2>";

$inventory_tables = [
    'inventory_sessions',
    'inventory_entries', 
    'inventory_users',
    'inventory_totals',
    'inventory_stock_changes',
    'previous_stock'
];

$existing_inventory_tables = [];
$missing_inventory_tables = [];

foreach ($inventory_tables as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $existing_inventory_tables[] = $table;
            echo "<p class='success'>✓ Inventurní tabulka '$table' existuje</p>";
        } else {
            $missing_inventory_tables[] = $table;
            echo "<p class='error'>✗ Inventurní tabulka '$table' neexistuje</p>";
        }
    } catch (Exception $e) {
        $missing_inventory_tables[] = $table;
        echo "<p class='error'>✗ Chyba při kontrole inventurní tabulky '$table': " . $e->getMessage() . "</p>";
    }
}

if (empty($missing_inventory_tables)) {
    echo "<p class='success'><strong>✓ Všechny inventurní tabulky existují</strong></p>";
} else {
    echo "<p class='warning'><strong>⚠ Některé inventurní tabulky chybí: " . implode(', ', $missing_inventory_tables) . "</strong></p>";
}
echo "</div>";

// Test API endpointů
echo "<div class='test-section'>";
echo "<h2>5. Test API endpointů</h2>";

$api_endpoints = [
    'api/simple_auth.php?action=check',
    'api/products.php?action=search&ean=test',
    'api/inventory.php?action=sessions',
    'api/users.php?action=list'
];

foreach ($api_endpoints as $endpoint) {
    echo "<h3>Test: $endpoint</h3>";

    $url = "http://localhost/PU/INVENTURA%20X/INVX1.5/$endpoint";

    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'timeout' => 10,
            'ignore_errors' => true
        ]
    ]);

    $response = @file_get_contents($url, false, $context);

    if ($response !== false) {
        $json_data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<p class='success'>✓ API endpoint odpovídá</p>";
            echo "<pre>" . json_encode($json_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        } else {
            echo "<p class='warning'>⚠ API endpoint odpovídá, ale nevrací platný JSON</p>";
            echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
        }
    } else {
        echo "<p class='error'>✗ API endpoint neodpovídá</p>";
    }
    echo "<hr>";
}
echo "</div>";

// Test přihlášení
echo "<div class='test-section'>";
echo "<h2>6. Test přihlášení</h2>";

// Zkusíme se přihlásit s výchozími údaji
$login_url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/simple_auth.php";
$login_data = json_encode([
    'action' => 'login',
    'username' => 'admin',
    'password' => 'admin123'
]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $login_data,
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$login_response = @file_get_contents($login_url, false, $context);

if ($login_response !== false) {
    $login_json = json_decode($login_response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        if (isset($login_json['success']) && $login_json['success']) {
            echo "<p class='success'>✓ Přihlášení s výchozími údaji (admin/admin123) úspěšné</p>";
        } else {
            echo "<p class='warning'>⚠ Přihlášení neúspěšné: " . ($login_json['error'] ?? 'Neznámá chyba') . "</p>";
        }
        echo "<pre>" . json_encode($login_json, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<p class='error'>✗ Neplatná odpověď z API přihlášení</p>";
        echo "<pre>" . htmlspecialchars(substr($login_response, 0, 500)) . "</pre>";
    }
} else {
    echo "<p class='error'>✗ API přihlášení neodpovídá</p>";
}
echo "</div>";

// Souhrn testu
echo "<div class='test-section'>";
echo "<h2>7. Souhrn testu</h2>";

$total_issues = count($missing_tables) + count($missing_inventory_tables);

if ($total_issues == 0) {
    echo "<p class='success'><strong>✓ Aplikace je připravena k použití!</strong></p>";
    echo "<p>Všechny potřebné komponenty jsou dostupné a funkční.</p>";
} else {
    echo "<p class='warning'><strong>⚠ Aplikace vyžaduje dokončení nastavení</strong></p>";
    echo "<p>Počet problémů k vyřešení: $total_issues</p>";

    if (!empty($missing_inventory_tables)) {
        echo "<p>Chybějící inventurní tabulky je třeba vytvořit spuštěním: <code>check_tables.php</code></p>";
    }
}

echo "<p><strong>Doporučené další kroky:</strong></p>";
echo "<ul>";
echo "<li>Otevřete aplikaci v prohlížeči: <a href='index.html' target='_blank'>index.html</a></li>";
echo "<li>Přihlaste se s údaji: admin / admin123</li>";
echo "<li>Vytvořte novou inventuru a otestujte přidávání produktů</li>";
echo "<li>Otestujte všechny funkce podle vašich požadavků</li>";
echo "</ul>";

echo "</div>";

echo "<p><em>Test dokončen: " . date('Y-m-d H:i:s') . "</em></p>";
?>
