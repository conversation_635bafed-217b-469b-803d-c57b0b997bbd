<?php
/**
 * Test funkcionalit inventury - produkty a inventurní záznamy
 */

// Spuštění session pouze pokud ještě není spuštěna
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>Test funkcionalit inventury</h1>";

require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

try {
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✓ Připojení k databázi úspěšné</p>";
    
    echo "<h2>1. Test přihlášení</h2>";
    
    $user = authenticateUser('admin', 'admin123');
    
    if ($user) {
        echo "<p style='color: green;'>✓ Přihlášení úspěšné - uživatel: " . $user['username'] . " (ID: " . $user['id'] . ")</p>";
    } else {
        echo "<p style='color: red;'>✗ Přihlášení se nezdařilo</p>";
        exit;
    }
    
    echo "<h2>2. Test API přihlášení</h2>";

    // Nejprve se přihlásíme přes API
    $loginUrl = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/auth.php?action=login";

    $loginData = json_encode([
        'username' => 'admin',
        'password' => 'admin123'
    ]);

    $loginContext = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $loginData
        ]
    ]);

    $loginResponse = file_get_contents($loginUrl, false, $loginContext);

    if ($loginResponse === false) {
        echo "<p style='color: red;'>✗ Chyba při přihlášení přes API</p>";
        exit;
    } else {
        $loginData = json_decode($loginResponse, true);

        if ($loginData && isset($loginData['success']) && $loginData['success']) {
            echo "<p style='color: green;'>✓ Přihlášení přes API úspěšné</p>";
        } else {
            echo "<p style='color: red;'>✗ Přihlášení přes API selhalo</p>";
            echo "<p>Odpověď: " . htmlspecialchars($loginResponse) . "</p>";
            exit;
        }
    }

    echo "<h2>3. Test API produktů</h2>";

    // Test vyhledání produktu
    $testEan = '8594000000000'; // Testovací EAN

    echo "<h3>Test vyhledání produktu s EAN: $testEan</h3>";

    $url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/products.php?action=search&ean=" . urlencode($testEan);

    // Získání session cookie z přihlášení
    $sessionCookie = '';
    if (isset($http_response_header)) {
        foreach ($http_response_header as $header) {
            if (strpos($header, 'Set-Cookie:') === 0) {
                $sessionCookie = substr($header, 12);
                break;
            }
        }
    }

    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => 'Content-Type: application/json' . ($sessionCookie ? "\r\nCookie: " . $sessionCookie : '')
        ]
    ]);

    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "<p style='color: red;'>✗ Chyba při volání API produktů</p>";
    } else {
        $data = json_decode($response, true);
        
        if ($data && isset($data['product'])) {
            echo "<p style='color: green;'>✓ Produkt nalezen:</p>";
            echo "<ul>";
            echo "<li>Název: " . ($data['product']['name'] ?? 'N/A') . "</li>";
            echo "<li>EAN: " . ($data['product']['ean_code'] ?? 'N/A') . "</li>";
            echo "<li>Aktuální stav: " . ($data['product']['current_stock'] ?? 'N/A') . "</li>";
            echo "<li>Cena: " . ($data['product']['pricesell'] ?? 'N/A') . "</li>";
            echo "</ul>";
            
            $testProduct = $data['product'];
        } else {
            echo "<p style='color: orange;'>⚠ Produkt s EAN $testEan nebyl nalezen</p>";
            echo "<p>Odpověď API: " . htmlspecialchars($response) . "</p>";
            
            // Zkusíme najít jakýkoliv produkt
            echo "<h3>Hledání jakéhokoliv produktu</h3>";
            
            $stmt = $pdo->query("SELECT code, name, units FROM products LIMIT 1");
            $anyProduct = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($anyProduct) {
                $testEan = $anyProduct['code'];
                echo "<p>Zkusím s EAN: $testEan</p>";
                
                $url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/products.php?action=search&ean=" . urlencode($testEan);
                $response = file_get_contents($url, false, $context);
                
                if ($response !== false) {
                    $data = json_decode($response, true);
                    if ($data && isset($data['product'])) {
                        echo "<p style='color: green;'>✓ Produkt nalezen s jiným EAN:</p>";
                        echo "<ul>";
                        echo "<li>Název: " . ($data['product']['name'] ?? 'N/A') . "</li>";
                        echo "<li>EAN: " . ($data['product']['ean_code'] ?? 'N/A') . "</li>";
                        echo "</ul>";
                        $testProduct = $data['product'];
                    }
                }
            }
        }
    }
    
    echo "<h2>3. Test API inventury</h2>";
    
    echo "<h3>Test získání inventurních relací</h3>";
    
    $url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/inventory.php?action=sessions";
    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "<p style='color: red;'>✗ Chyba při volání API inventury</p>";
    } else {
        $data = json_decode($response, true);
        
        if ($data && isset($data['sessions'])) {
            echo "<p style='color: green;'>✓ Inventurní relace načteny: " . count($data['sessions']) . " relací</p>";
            
            if (count($data['sessions']) > 0) {
                $testSession = $data['sessions'][0];
                echo "<p>Testovací relace ID: " . $testSession['id'] . "</p>";
                
                echo "<h3>Test získání inventurních záznamů</h3>";
                
                $url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/inventory.php?action=entries&session_id=" . $testSession['id'];
                $response = file_get_contents($url, false, $context);
                
                if ($response !== false) {
                    $data = json_decode($response, true);
                    
                    if ($data && isset($data['entries'])) {
                        echo "<p style='color: green;'>✓ Inventurní záznamy načteny: " . count($data['entries']) . " záznamů</p>";
                        
                        if (count($data['entries']) > 0) {
                            echo "<p>Ukázka prvního záznamu:</p>";
                            $entry = $data['entries'][0];
                            echo "<ul>";
                            echo "<li>ID: " . ($entry['id'] ?? 'N/A') . "</li>";
                            echo "<li>Produkt: " . ($entry['product_name'] ?? 'N/A') . "</li>";
                            echo "<li>EAN: " . ($entry['ean_code'] ?? 'N/A') . "</li>";
                            echo "<li>Zadané množství: " . ($entry['zadane_mnozstvi'] ?? 'N/A') . "</li>";
                            echo "</ul>";
                        }
                    } else {
                        echo "<p style='color: orange;'>⚠ Žádné inventurní záznamy</p>";
                        echo "<p>Odpověď: " . htmlspecialchars($response) . "</p>";
                    }
                } else {
                    echo "<p style='color: red;'>✗ Chyba při načítání inventurních záznamů</p>";
                }
            }
        } else {
            echo "<p style='color: orange;'>⚠ Žádné inventurní relace</p>";
            echo "<p>Odpověď: " . htmlspecialchars($response) . "</p>";
        }
    }
    
    echo "<h2>4. Test vytvoření inventurní relace</h2>";
    
    $url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/inventory.php?action=sessions";
    
    $postData = json_encode([]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $postData
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "<p style='color: red;'>✗ Chyba při vytváření inventurní relace</p>";
    } else {
        $data = json_decode($response, true);
        
        if ($data && isset($data['success']) && $data['success']) {
            echo "<p style='color: green;'>✓ Inventurní relace vytvořena s ID: " . $data['session_id'] . "</p>";
        } else {
            echo "<p style='color: red;'>✗ Chyba při vytváření inventurní relace</p>";
            echo "<p>Odpověď: " . htmlspecialchars($response) . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
}

echo "<h2>Závěr</h2>";
echo "<p><a href='index.html'>Zkusit hlavní aplikaci</a></p>";
?>
