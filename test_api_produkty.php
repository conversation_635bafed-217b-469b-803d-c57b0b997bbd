<?php
/**
 * Test API produkty - bez nutnosti přihlášení
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='cs'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Test API produkty</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".ok { color: green; font-weight: bold; }";
echo ".error { color: red; font-weight: bold; }";
echo ".warning { color: orange; font-weight: bold; }";
echo ".test { margin: 10px 0; padding: 10px; border-left: 3px solid #ccc; }";
echo "pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔍 Test API produkty</h1>";

// Načtení konfigurace databáze
try {
    $config = require_once 'config/database.php';
    echo "<div class='test'>";
    echo "<h3>Konfigurace databáze</h3>";
    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Konfigurace načtena";
    echo "</div>";
} catch (Exception $e) {
    echo "<div class='test'>";
    echo "<h3>Konfigurace databáze</h3>";
    echo "<span class='error'>✗ CHYBA</span> - " . $e->getMessage();
    echo "</div>";
    exit;
}

// Test připojení k databázi
try {
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    echo "<div class='test'>";
    echo "<h3>Připojení k databázi</h3>";
    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Připojení k databázi funguje";
    echo "</div>";
} catch (Exception $e) {
    echo "<div class='test'>";
    echo "<h3>Připojení k databázi</h3>";
    echo "<span class='error'>✗ CHYBA</span> - " . $e->getMessage();
    echo "</div>";
    exit;
}

// Test existence tabulky products
echo "<div class='test'>";
echo "<h3>Kontrola tabulky products</h3>";
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'products'");
    if ($stmt->rowCount() > 0) {
        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Tabulka products existuje<br>";
        
        // Zkontrolujeme strukturu tabulky
        $stmt = $pdo->query("DESCRIBE products");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<strong>Sloupce v tabulce:</strong> " . implode(', ', $columns) . "<br>";
        
        // Zkontrolujeme počet produktů
        $stmt = $pdo->query("SELECT COUNT(*) FROM products");
        $count = $stmt->fetchColumn();
        echo "<strong>Počet produktů:</strong> $count";
        
        if ($count > 0) {
            // Zobrazíme několik ukázkových produktů
            $stmt = $pdo->query("SELECT code, name FROM products LIMIT 5");
            $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<br><strong>Ukázkové produkty:</strong><br>";
            foreach ($products as $product) {
                echo "- {$product['code']}: {$product['name']}<br>";
            }
        }
    } else {
        echo "<span class='error'>✗ CHYBA</span> - Tabulka products neexistuje";
    }
} catch (Exception $e) {
    echo "<span class='error'>✗ CHYBA</span> - " . $e->getMessage();
}
echo "</div>";

// Test přímého vyhledávání produktu v databázi
echo "<div class='test'>";
echo "<h3>Test vyhledávání produktu v databázi</h3>";
try {
    // Najdeme první produkt s EAN kódem
    $stmt = $pdo->query("SELECT code, name FROM products WHERE code IS NOT NULL AND code != '' LIMIT 1");
    $testProduct = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testProduct) {
        $testEan = $testProduct['code'];
        echo "<strong>Testovací EAN:</strong> $testEan<br>";
        
        // Provedeme vyhledávání jako v API
        $stmt = $pdo->prepare("
            SELECT
                p.id,
                p.code AS ean_code,
                p.name,
                c.name AS category,
                p.pricebuy,
                t.rate AS tax_rate,
                p.pricesell,
                COALESCE(s.units, 0) AS current_stock
            FROM
                products p
            LEFT JOIN
                categories c ON p.category = c.id
            LEFT JOIN
                taxes t ON p.taxcat = t.id
            LEFT JOIN
                stockcurrent s ON p.id = s.product
            WHERE
                p.code = :ean_code
        ");
        
        $stmt->execute(['ean_code' => $testEan]);
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($product) {
            echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Produkt nalezen<br>";
            echo "<pre>" . print_r($product, true) . "</pre>";
        } else {
            echo "<span class='error'>✗ CHYBA</span> - Produkt nenalezen";
        }
    } else {
        echo "<span class='warning'>⚠ VAROVÁNÍ</span> - Žádné produkty s EAN kódem v databázi";
    }
} catch (Exception $e) {
    echo "<span class='error'>✗ CHYBA</span> - " . $e->getMessage();
}
echo "</div>";

// Test API s testovacím režimem
echo "<div class='test'>";
echo "<h3>Test API produkty s testovacím režimem</h3>";

// Nastavíme testovací režim v session
session_start();
$_SESSION['test_mode'] = true;

try {
    // Najdeme testovací EAN
    $stmt = $pdo->query("SELECT code FROM products WHERE code IS NOT NULL AND code != '' LIMIT 1");
    $testProduct = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($testProduct) {
        $testEan = $testProduct['code'];
        
        // Zavoláme API
        $url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/products.php?action=search&ean=" . urlencode($testEan);
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 10,
                'ignore_errors' => true,
                'header' => "Cookie: " . session_name() . "=" . session_id()
            ]
        ]);
        
        $response = @file_get_contents($url, false, $context);
        
        if ($response !== false) {
            $data = json_decode($response, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                if (isset($data['product'])) {
                    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - API produkty funguje<br>";
                    echo "<strong>Testovací EAN:</strong> $testEan<br>";
                    echo "<strong>Odpověď API:</strong><br>";
                    echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                } else {
                    echo "<span class='warning'>⚠ VAROVÁNÍ</span> - API odpovídá, ale produkt nenalezen<br>";
                    echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                }
            } else {
                echo "<span class='error'>✗ CHYBA</span> - API nevrací platný JSON<br>";
                echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
            }
        } else {
            echo "<span class='error'>✗ CHYBA</span> - API neodpovídá";
        }
    } else {
        echo "<span class='warning'>⚠ VAROVÁNÍ</span> - Žádné produkty k testování";
    }
} catch (Exception $e) {
    echo "<span class='error'>✗ CHYBA</span> - " . $e->getMessage();
}

// Vyčistíme testovací režim
unset($_SESSION['test_mode']);
echo "</div>";

echo "<hr>";
echo "<h2>📋 Doporučení</h2>";
echo "<ul>";
echo "<li>Pokud API nefunguje, zkontrolujte, zda jsou soubory v adresáři api/ dostupné</li>";
echo "<li>Ověřte, že webový server má oprávnění ke čtení souborů</li>";
echo "<li>Zkontrolujte error log webového serveru pro další informace</li>";
echo "<li>Ujistěte se, že v databázi jsou nějaké produkty s EAN kódy</li>";
echo "</ul>";

echo "<p><a href='rychly_test.php'>← Zpět na rychlý test</a></p>";

echo "</body>";
echo "</html>";
?>
