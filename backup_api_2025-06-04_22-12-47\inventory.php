<?php
/**
 * Inventura API
 *
 * Tento soubor zpracovává API endpointy související s inventurou.
 */

// Spuštění session pouze pokud ještě není spuštěna
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/../utils/database.php';
require_once __DIR__ . '/../utils/auth.php';
require_once __DIR__ . '/../utils/validation.php';

// Nastavení typu obsahu na JSON
header('Content-Type: application/json');

// Získání metody požadavku
$method = $_SERVER['REQUEST_METHOD'];

// Získání cesty požadavku
$path = $_SERVER['PATH_INFO'] ?? '/';

// Debug informace
error_log("API inventory.php - metoda: $method, cesta: $path, akce: " . ($_GET['action'] ?? 'N/A'));
error_log("API inventory.php - session data: " . print_r($_SESSION ?? [], true));

// Kontrola, zda je uživatel přihlášen (pouze pokud není testovací prostředí)
if (!isLoggedIn() && !isset($_SESSION['test_mode'])) {
    error_log("API inventory.php - uživatel není přihlášen");
    sendResponse(['error' => 'Neautorizovaný přístup'], 401);
    exit;
}

// Získání akce z query stringu (podpora obou 'action' a 'endpoint' pro zpětnou kompatibilitu)
$action = $_GET['action'] ?? $_GET['endpoint'] ?? '';

// Zpracování různých akcí
switch ($action) {
    case 'sessions':
        handleSessions();
        break;

    case 'session':
        handleSession();
        break;

    case 'entries':
        handleEntries();
        break;

    case 'entry':
        handleEntry();
        break;

    case 'complete':
        handleComplete();
        break;

    case 'export':
        handleExport();
        break;

    case 'inventory':
        handleInventory();
        break;

    case 'active-session':
        handleActiveSession();
        break;

    case 'refresh':
        handleRefresh();
        break;

    case 'total-entries':
        handleTotalEntries();
        break;

    case 'missing-products':
        handleMissingProducts();
        break;

    default:
        // Pokud není zadána akce, zpracujeme podle metody a cesty
        if (empty($action)) {
            // Základní volání bez parametrů - zpracujeme jako sessions
            if ($method === 'GET') {
                // GET bez parametrů = získání seznamu inventur
                getSessions();
            } elseif ($method === 'POST') {
                // POST bez parametrů = vytvoření nové inventury
                createSession();
            } else {
                sendResponse(['error' => 'Metoda není povolena'], 405);
            }
        } else {
            // Zpětná kompatibilita - zkusíme zpracovat podle cesty
            switch ($path) {
                case '/sessions':
                    handleSessions();
                    break;

                case '/session':
                    handleSession();
                    break;

                case '/entries':
                    handleEntries();
                    break;

                case '/entry':
                    handleEntry();
                    break;

                case '/complete':
                    handleComplete();
                    break;

                case '/export':
                    handleExport();
                    break;

                default:
                    // Kontrola, zda cesta odpovídá vzoru /session/{id}/inventory
                    if (preg_match('#^/session/(\d+)/inventory$#', $path, $matches)) {
                        $_GET['session_id'] = $matches[1];
                        handleInventory();
                    } else {
                        sendResponse(['error' => 'Endpoint nenalezen'], 404);
                    }
                    break;
            }
        }
        break;
}

/**
 * Zpracování požadavků na inventurní relace
 */
function handleSessions() {
    global $method;

    switch ($method) {
        case 'GET':
            // Získání seznamu inventurních relací
            getSessions();
            break;

        case 'POST':
            // Vytvoření nové inventurní relace
            createSession();
            break;

        default:
            sendResponse(['error' => 'Metoda není povolena'], 405);
            break;
    }
}

/**
 * Zpracování požadavků na konkrétní inventurní relaci
 */
function handleSession() {
    global $method;

    // Kontrola, zda je požadována emulace metody PUT nebo DELETE přes POST
    $emulatedMethod = null;
    if ($method === 'POST') {
        // Získání JSON vstupu
        $input = json_decode(file_get_contents('php://input'), true);
        if ($input && isset($input['_method'])) {
            $emulatedMethod = strtoupper($input['_method']);
        }
    }

    switch ($method) {
        case 'GET':
            // Získání detailů inventurní relace
            getSession();
            break;

        case 'PUT':
            // Aktualizace inventurní relace
            updateSession();
            break;

        case 'POST':
            if ($emulatedMethod === 'PUT') {
                // Emulace metody PUT přes POST
                updateSession();
            } else if ($emulatedMethod === 'DELETE') {
                // Emulace metody DELETE přes POST
                deleteSession();
            } else {
                sendResponse(['error' => 'Metoda není povolena'], 405);
            }
            break;

        case 'DELETE':
            // Smazání inventurní relace
            deleteSession();
            break;

        default:
            sendResponse(['error' => 'Metoda není povolena'], 405);
            break;
    }
}

/**
 * Zpracování požadavků na inventurní záznamy
 */
function handleEntries() {
    global $method;

    switch ($method) {
        case 'GET':
            // Získání seznamu inventurních záznamů
            getEntries();
            break;

        case 'POST':
            // Vytvoření nového inventurního záznamu
            createEntry();
            break;

        default:
            sendResponse(['error' => 'Metoda není povolena'], 405);
            break;
    }
}

/**
 * Zpracování požadavků na konkrétní inventurní záznam
 */
function handleEntry() {
    global $method;

    // Kontrola, zda je požadováno smazání záznamu přes POST
    $isDeleteAction = false;

    // Kontrola, zda je požadována emulace metody DELETE přes POST
    $emulatedMethod = null;
    if ($method === 'POST') {
        // Získání JSON vstupu
        $postData = file_get_contents('php://input');
        error_log("handleEntry - POST data: " . $postData);

        $input = json_decode($postData, true);
        error_log("handleEntry - dekódovaný JSON vstup: " . print_r($input, true));

        if ($input && isset($input['_method'])) {
            $emulatedMethod = strtoupper($input['_method']);
            $isDeleteAction = ($emulatedMethod === 'DELETE');
            error_log("handleEntry - emulovaná metoda: $emulatedMethod, isDeleteAction: " . ($isDeleteAction ? 'true' : 'false'));
        } else {
            // Zpětná kompatibilita - kontrola parametrů v URL
            $isDeleteAction = (
                (isset($_GET['action']) && $_GET['action'] === 'delete') ||
                (isset($_GET['delete']) && $_GET['delete'] === '1')
            );
            error_log("handleEntry - zpětná kompatibilita, isDeleteAction: " . ($isDeleteAction ? 'true' : 'false'));
        }
    }

    error_log("handleEntry - URL: " . $_SERVER['REQUEST_URI']);
    error_log("handleEntry - metoda: $method, emulatedMethod: " . ($emulatedMethod ?? 'null') . ", isDeleteAction: " . ($isDeleteAction ? 'true' : 'false') . ", GET parametry: " . print_r($_GET, true));

    switch ($method) {
        case 'GET':
            // Získání detailů inventurního záznamu
            getEntry();
            break;

        case 'PUT':
            // Aktualizace inventurního záznamu
            updateEntry();
            break;

        case 'DELETE':
            // Smazání inventurního záznamu
            deleteEntry();
            break;

        case 'POST':
            if ($isDeleteAction) {
                // Smazání inventurního záznamu přes POST
                deleteEntry();
            } else {
                sendResponse(['error' => 'Metoda není povolena'], 405);
            }
            break;

        default:
            sendResponse(['error' => 'Metoda není povolena'], 405);
            break;
    }
}

/**
 * Zpracování požadavku na dokončení inventury
 */
function handleComplete() {
    global $method;

    if ($method !== 'POST') {
        sendResponse(['error' => 'Metoda není povolena'], 405);
        return;
    }

    // Kontrola, zda je uživatel admin nebo manažer
    if (!isAdminOrManager()) {
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    // Získání JSON vstupu
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        sendResponse(['error' => 'Neplatný JSON'], 400);
        return;
    }

    // Validace vstupu
    $schema = [
        'session_id' => ['type' => 'int', 'required' => true]
    ];

    $validation = validateData($input, $schema);

    if (!$validation['valid']) {
        sendResponse(['error' => 'Validace selhala', 'details' => $validation['errors']], 400);
        return;
    }

    $sessionId = $validation['data']['session_id'];

    // Kontrola, zda relace existuje
    $pdo = getDbConnection();

    $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = :id");
    $stmt->execute(['id' => $sessionId]);

    $session = $stmt->fetch();

    if (!$session) {
        sendResponse(['error' => 'Inventurní relace nenalezena'], 404);
        return;
    }

    // Kontrola, zda relace není již dokončena
    if ($session['status'] === 'completed') {
        sendResponse(['error' => 'Inventurní relace je již dokončena'], 400);
        return;
    }

    try {
        // Začátek transakce
        $pdo->beginTransaction();

        // Aktualizace stavu zásob na základě inventurních záznamů
        $stmt = $pdo->prepare("
            SELECT
                ie.product_id,
                SUM(ie.zadane_mnozstvi) AS total_zadane_mnozstvi
            FROM
                inventory_entries ie
            WHERE
                ie.session_id = :session_id
                AND ie.status = 'active'
            GROUP BY
                ie.product_id
        ");

        $stmt->execute(['session_id' => $sessionId]);

        $products = $stmt->fetchAll();

        foreach ($products as $product) {
            // Aktualizace stavu zásob v stockcurrent
            $updateStmt = $pdo->prepare("
                UPDATE stockcurrent
                SET units = :units
                WHERE product = :product_id
            ");

            $updateStmt->execute([
                'units' => $product['total_zadane_mnozstvi'],
                'product_id' => $product['product_id']
            ]);

            // Pokud produkt nemá záznam v stockcurrent, vytvoříme ho
            if ($updateStmt->rowCount() === 0) {
                $insertStmt = $pdo->prepare("
                    INSERT INTO stockcurrent (product, location, units)
                    VALUES (:product_id, 0, :units)
                ");

                $insertStmt->execute([
                    'product_id' => $product['product_id'],
                    'units' => $product['total_zadane_mnozstvi']
                ]);
            }
        }

        // Aktualizace stavu relace na dokončeno
        $updateSessionStmt = $pdo->prepare("
            UPDATE inventory_sessions
            SET status = 'completed', end_time = NOW()
            WHERE id = :id
        ");

        $updateSessionStmt->execute(['id' => $sessionId]);

        // Commit transakce
        $pdo->commit();

        sendResponse(['success' => true, 'message' => 'Inventura byla úspěšně dokončena']);
    } catch (Exception $e) {
        // Rollback transakce v případě chyby
        $pdo->rollBack();

        error_log("Chyba při dokončování inventury: " . $e->getMessage());
        sendResponse(['error' => 'Došlo k chybě při dokončování inventury'], 500);
    }
}

/**
 * Zpracování požadavku na export inventury
 */
function handleExport() {
    global $method;

    error_log("handleExport - začátek funkce");
    error_log("handleExport - metoda: " . $method);
    error_log("handleExport - GET parametry: " . print_r($_GET, true));

    if ($method !== 'GET') {
        error_log("handleExport - nepovolená metoda: " . $method);
        sendResponse(['error' => 'Metoda není povolena'], 405);
        return;
    }

    // Získání ID relace
    $sessionId = $_GET['session_id'] ?? null;
    error_log("handleExport - session_id: " . $sessionId);

    if (!$sessionId) {
        error_log("handleExport - chybí ID relace");
        sendResponse(['error' => 'ID relace je povinné'], 400);
        return;
    }

    // Validace ID relace
    $sessionId = validateInt($sessionId);
    error_log("handleExport - validované session_id: " . $sessionId);

    if (!$sessionId) {
        error_log("handleExport - neplatné ID relace");
        sendResponse(['error' => 'Neplatné ID relace'], 400);
        return;
    }

    // Kontrola, zda relace existuje
    $pdo = getDbConnection();

    $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = :id");
    $stmt->execute(['id' => $sessionId]);

    $session = $stmt->fetch();

    if (!$session) {
        sendResponse(['error' => 'Inventurní relace nenalezena'], 404);
        return;
    }

    // Kontrola oprávnění - běžný uživatel může exportovat pouze své záznamy
    $user = getCurrentUser();

    if (!isAdminOrManager() && $session['user_id'] !== $user['id']) {
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    // Získání inventurních záznamů - sčítání záznamů od všech uživatelů
    $stmt = $pdo->prepare("
        SELECT
            p.id AS product_id,
            p.code AS ean_code,
            p.name AS product_name,
            c.name AS category,
            p.pricebuy,
            t.rate AS tax_rate,
            p.pricesell,
            COALESCE(s.units, 0) AS current_stock,
            SUM(ie.zadane_mnozstvi) AS zadane_mnozstvi,
            (SUM(ie.zadane_mnozstvi) - COALESCE(s.units, 0)) AS difference,
            GROUP_CONCAT(DISTINCT u.username) AS users,
            MAX(ie.last_updated) AS last_updated
        FROM
            inventory_entries ie
        JOIN
            products p ON ie.product_id = p.id
        LEFT JOIN
            categories c ON p.category = c.id
        LEFT JOIN
            taxes t ON p.taxcat = t.id
        LEFT JOIN
            stockcurrent s ON p.id = s.product
        JOIN
            inventory_users u ON ie.user_id = u.id
        WHERE
            ie.session_id = :session_id
            AND ie.status = 'active'
        GROUP BY
            p.id, p.code, p.name, c.name, p.pricebuy, t.rate, p.pricesell, s.units
        ORDER BY
            p.name
    ");

    error_log("handleExport - SQL dotaz: " . $stmt->queryString);

    $params = ['session_id' => $sessionId];

    $stmt->execute($params);
    error_log("handleExport - SQL dotaz proveden");

    $entries = $stmt->fetchAll();
    error_log("handleExport - počet záznamů: " . count($entries));

    // Příprava dat pro export
    $exportData = [];

    foreach ($entries as $entry) {
        $exportData[] = [
            'product_id' => $entry['product_id'],
            'ean_code' => $entry['ean_code'],
            'EAN' => $entry['ean_code'], // Pro kompatibilitu s původním formátem
            'product_name' => $entry['product_name'],
            'Název produktu' => $entry['product_name'], // Pro kompatibilitu s původním formátem
            'category' => $entry['category'],
            'Kategorie' => $entry['category'], // Pro kompatibilitu s původním formátem
            'pricebuy' => $entry['pricebuy'],
            'Nákupní cena' => $entry['pricebuy'], // Pro kompatibilitu s původním formátem
            'tax_rate' => $entry['tax_rate'],
            'DPH (%)' => $entry['tax_rate'], // Pro kompatibilitu s původním formátem
            'pricesell' => $entry['pricesell'],
            'Prodejní cena bez DPH' => $entry['pricesell'], // Pro kompatibilitu s původním formátem
            'price_with_tax' => $entry['pricesell'] * (1 + $entry['tax_rate']),
            'Prodejní cena s DPH' => $entry['pricesell'] * (1 + $entry['tax_rate']), // Pro kompatibilitu s původním formátem
            'current_stock' => $entry['current_stock'],
            'Aktuální stav' => $entry['current_stock'], // Pro kompatibilitu s původním formátem
            'zadane_mnozstvi' => $entry['zadane_mnozstvi'],
            'Zadané množství' => $entry['zadane_mnozstvi'], // Pro kompatibilitu s původním formátem
            'difference' => $entry['difference'],
            'Rozdíl' => $entry['difference'], // Pro kompatibilitu s původním formátem
            'users' => $entry['users'],
            'Uživatelé' => $entry['users'], // Pro kompatibilitu s původním formátem
            'last_updated' => $entry['last_updated'],
            'Poslední aktualizace' => $entry['last_updated'] // Pro kompatibilitu s původním formátem
        ];
    }

    error_log("handleExport - data připravena pro export: " . count($exportData) . " záznamů");
    error_log("handleExport - první záznam (pokud existuje): " . (count($exportData) > 0 ? print_r($exportData[0], true) : "žádný záznam"));

    // Přidáme data také do klíče 'entries' pro kompatibilitu s klientským kódem
    $response = [
        'data' => $exportData,
        'entries' => $exportData
    ];

    error_log("handleExport - odesílám odpověď");
    sendResponse($response);
}

/**
 * Získání seznamu inventurních relací
 */
function getSessions() {
    $pdo = getDbConnection();

    // Běžní uživatelé vidí pouze své relace, admin a manažeři vidí všechny
    $user = getCurrentUser();

    $sql = "
        SELECT
            s.id,
            s.start_time,
            s.end_time,
            s.status,
            u.username AS user,
            (
                SELECT COUNT(*)
                FROM inventory_entries e
                WHERE e.session_id = s.id AND e.status = 'active'
            ) AS entry_count
        FROM
            inventory_sessions s
        JOIN
            inventory_users u ON s.user_id = u.id
    ";

    $params = [];

    // Filtrování podle statusu, pokud je zadán
    $status = $_GET['status'] ?? null;

    if ($status) {
        $sql .= " WHERE s.status = :status";
        $params['status'] = $status;
    } else {
        $sql .= " WHERE 1=1"; // Vždy pravdivá podmínka pro snadnější přidávání dalších podmínek
    }

    // Běžný uživatel vidí všechny aktivní inventury, ale pouze své dokončené a zrušené
    if (!isAdminOrManager()) {
        $sql .= " AND (s.status = 'active' OR s.user_id = :user_id)";
        $params['user_id'] = $user['id'];
    }

    $sql .= " ORDER BY s.start_time DESC";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);

    $sessions = $stmt->fetchAll();

    sendResponse(['sessions' => $sessions]);
}

/**
 * Zpracování požadavku na aktivní inventurní relaci
 */
function handleActiveSession() {
    global $method;

    if ($method !== 'GET') {
        sendResponse(['error' => 'Metoda není povolena'], 405);
        return;
    }

    // Získání aktivní relace pro přihlášeného uživatele
    $user = getCurrentUser();
    $pdo = getDbConnection();

    // Běžní uživatelé vidí pouze své relace, admin a manažeři vidí první aktivní relaci
    $sql = "
        SELECT
            s.id,
            s.start_time,
            s.end_time,
            s.status,
            u.username AS user,
            (
                SELECT COUNT(*)
                FROM inventory_entries e
                WHERE e.session_id = s.id AND e.status = 'active'
            ) AS entry_count
        FROM
            inventory_sessions s
        JOIN
            inventory_users u ON s.user_id = u.id
        WHERE
            s.status = 'active'
    ";

    $params = [];

    // Běžný uživatel vidí všechny aktivní inventury
    // Není potřeba filtrovat podle user_id, protože už filtrujeme podle status = 'active'

    $sql .= " ORDER BY s.start_time DESC LIMIT 1";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);

    $session = $stmt->fetch();

    if ($session) {
        sendResponse(['session' => $session]);
    } else {
        sendResponse(['session' => null]);
    }
}

/**
 * Vytvoření nové inventurní relace
 */
function createSession() {
    error_log("createSession - začátek funkce");
    error_log("createSession - session_id: " . session_id());
    error_log("createSession - session data: " . print_r($_SESSION ?? [], true));

    // Získání JSON vstupu
    $inputRaw = file_get_contents('php://input');
    error_log("createSession - raw input: " . $inputRaw);

    $input = json_decode($inputRaw, true);
    error_log("createSession - decoded input: " . print_r($input, true));

    // Kontrola, zda je vstup platný JSON (může být i prázdný objekt)
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("createSession - JSON chyba: " . json_last_error_msg());
        sendResponse(['error' => 'Neplatný JSON: ' . json_last_error_msg()], 400);
        return;
    }

    // Kontrola přihlášení
    if (!isLoggedIn()) {
        error_log("createSession - uživatel není přihlášen podle isLoggedIn()");
        sendResponse(['error' => 'Uživatel není přihlášen'], 401);
        return;
    }

    // Získání aktuálně přihlášeného uživatele
    $user = getCurrentUser();
    error_log("createSession - getCurrentUser() vrátil: " . print_r($user, true));

    if (!$user) {
        error_log("createSession - getCurrentUser() vrátil null");
        sendResponse(['error' => 'Uživatel není přihlášen'], 401);
        return;
    }

    if (!isset($user['id'])) {
        error_log("createSession - uživatel nemá ID: " . print_r($user, true));
        sendResponse(['error' => 'Neplatná data uživatele'], 400);
        return;
    }

    error_log("createSession - použit přihlášený uživatel: " . $user['username'] . " (ID: " . $user['id'] . ")");

    $pdo = getDbConnection();

    try {
        $stmt = $pdo->prepare("
            INSERT INTO inventory_sessions (user_id)
            VALUES (:user_id)
        ");

        $stmt->execute(['user_id' => $user['id']]);

        $sessionId = $pdo->lastInsertId();
        error_log("createSession - vytvořena relace s ID: " . $sessionId);

        sendResponse([
            'success' => true,
            'session_id' => $sessionId,
            'message' => 'Inventurní relace byla úspěšně vytvořena'
        ]);
    } catch (Exception $e) {
        error_log("Chyba při vytváření inventurní relace: " . $e->getMessage());
        error_log("createSession - stack trace: " . $e->getTraceAsString());
        error_log("createSession - SQL error info: " . print_r($stmt->errorInfo(), true));
        sendResponse([
            'error' => 'Došlo k chybě při vytváření inventurní relace',
            'debug_message' => $e->getMessage(),
            'debug_file' => $e->getFile(),
            'debug_line' => $e->getLine()
        ], 500);
    }
}

/**
 * Získání detailů inventurní relace
 */
function getSession() {
    global $pathParts;

    // Získání ID relace z URL nebo z GET parametru
    $sessionId = null;

    // Pokud je ID v URL (např. /inventory/session/123)
    if (isset($pathParts[2]) && is_numeric($pathParts[2])) {
        $sessionId = $pathParts[2];
    }
    // Pokud je ID v GET parametru (např. ?id=123)
    else if (isset($_GET['id'])) {
        $sessionId = $_GET['id'];
    }

    if (!$sessionId) {
        sendResponse(['error' => 'ID relace je povinné'], 400);
        return;
    }

    // Validace ID relace
    $sessionId = validateInt($sessionId);

    if (!$sessionId) {
        sendResponse(['error' => 'Neplatné ID relace'], 400);
        return;
    }

    $pdo = getDbConnection();

    // Kontrola oprávnění - běžný uživatel může vidět pouze své relace
    $user = getCurrentUser();

    $sql = "
        SELECT
            s.id,
            s.start_time,
            s.end_time,
            s.status,
            u.username AS user,
            u.id AS user_id,
            (
                SELECT COUNT(*)
                FROM inventory_entries e
                WHERE e.session_id = s.id AND e.status = 'active'
            ) AS entry_count
        FROM
            inventory_sessions s
        JOIN
            inventory_users u ON s.user_id = u.id
        WHERE
            s.id = :id
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute(['id' => $sessionId]);

    $session = $stmt->fetch();

    if (!$session) {
        sendResponse(['error' => 'Inventurní relace nenalezena'], 404);
        return;
    }

    // Běžný uživatel může vidět všechny aktivní inventury, ale pouze své dokončené a zrušené
    if (!isAdminOrManager() && $session['status'] !== 'active' && $session['user_id'] != $user['id']) {
        error_log("getSession - nedostatečná oprávnění, user_id: " . $user['id'] . ", session user_id: " . $session['user_id'] . ", status: " . $session['status']);
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    sendResponse(['session' => $session]);
}

/**
 * Aktualizace inventurní relace
 */
function updateSession() {
    // Získání JSON vstupu
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        sendResponse(['error' => 'Neplatný JSON'], 400);
        return;
    }

    // Validace vstupu
    $schema = [
        'id' => ['type' => 'int', 'required' => true],
        'status' => ['type' => 'string', 'required' => false]
    ];

    $validation = validateData($input, $schema);

    if (!$validation['valid']) {
        sendResponse(['error' => 'Validace selhala', 'details' => $validation['errors']], 400);
        return;
    }

    $sessionId = $validation['data']['id'];
    $status = $validation['data']['status'] ?? null;

    // Kontrola, zda relace existuje
    $pdo = getDbConnection();

    $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = :id");
    $stmt->execute(['id' => $sessionId]);

    $session = $stmt->fetch();

    if (!$session) {
        sendResponse(['error' => 'Inventurní relace nenalezena'], 404);
        return;
    }

    // Získání aktuálního uživatele
    $user = getCurrentUser();

    // Kontrola oprávnění - běžný uživatel může upravovat pouze své relace
    if (!isAdminOrManager() && $session['user_id'] != $user['id']) {
        error_log("updateSession - nedostatečná oprávnění, user_id: " . $user['id'] . ", session user_id: " . $session['user_id']);
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    // Aktualizace relace
    $updateFields = [];
    $params = ['id' => $sessionId];

    if ($status !== null) {
        $updateFields[] = "status = :status";
        $params['status'] = $status;

        // Pokud je status 'completed', nastavíme end_time
        if ($status === 'completed') {
            $updateFields[] = "end_time = NOW()";
        }
    }

    if (empty($updateFields)) {
        sendResponse(['error' => 'Žádná data k aktualizaci'], 400);
        return;
    }

    $sql = "UPDATE inventory_sessions SET " . implode(", ", $updateFields) . " WHERE id = :id";

    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        sendResponse([
            'success' => true,
            'message' => 'Inventurní relace byla úspěšně aktualizována'
        ]);
    } catch (Exception $e) {
        error_log("Chyba při aktualizaci inventurní relace: " . $e->getMessage());
        sendResponse(['error' => 'Došlo k chybě při aktualizaci inventurní relace'], 500);
    }
}

/**
 * Smazání inventurní relace
 */
function deleteSession() {
    global $method;

    // Získání ID relace - buď z GET parametru nebo z JSON vstupu
    $sessionId = null;

    if ($method === 'POST') {
        // Získání JSON vstupu
        $input = json_decode(file_get_contents('php://input'), true);
        if ($input && isset($input['id'])) {
            $sessionId = $input['id'];
        }
    } else {
        $sessionId = $_GET['id'] ?? null;
    }

    if (!$sessionId) {
        sendResponse(['error' => 'ID relace je povinné'], 400);
        return;
    }

    // Validace ID relace
    $sessionId = validateInt($sessionId);

    if (!$sessionId) {
        sendResponse(['error' => 'Neplatné ID relace'], 400);
        return;
    }

    // Kontrola, zda relace existuje
    $pdo = getDbConnection();

    $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = :id");
    $stmt->execute(['id' => $sessionId]);

    $session = $stmt->fetch();

    if (!$session) {
        sendResponse(['error' => 'Inventurní relace nenalezena'], 404);
        return;
    }

    // Kontrola oprávnění - admin může mazat všechny relace, manažer může mazat pouze dokončené nebo zrušené relace
    $user = getCurrentUser();

    if (!isAdmin() && (!isManager() || $session['status'] === 'active')) {
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    // Smazání relace a souvisejících záznamů
    try {
        // Začátek transakce
        $pdo->beginTransaction();

        // Nejprve smažeme související záznamy
        $stmt = $pdo->prepare("DELETE FROM inventory_entries WHERE session_id = :id");
        $stmt->execute(['id' => $sessionId]);

        // Smažeme záznamy o změnách stavu zásob
        $stmt = $pdo->prepare("DELETE FROM inventory_stock_changes WHERE session_id = :id");
        $stmt->execute(['id' => $sessionId]);

        // Nakonec smažeme samotnou relaci
        $stmt = $pdo->prepare("DELETE FROM inventory_sessions WHERE id = :id");
        $stmt->execute(['id' => $sessionId]);

        // Potvrzení transakce
        $pdo->commit();

        sendResponse([
            'success' => true,
            'message' => 'Inventurní relace byla úspěšně smazána'
        ]);
    } catch (Exception $e) {
        // Rollback v případě chyby
        $pdo->rollBack();

        error_log("Chyba při mazání inventurní relace: " . $e->getMessage());
        sendResponse(['error' => 'Došlo k chybě při mazání inventurní relace: ' . $e->getMessage()], 500);
    }
}

/**
 * Získání seznamu inventurních záznamů
 */
function getEntries() {
    // Získání ID relace
    $sessionId = $_GET['session_id'] ?? null;

    if (!$sessionId) {
        sendResponse(['error' => 'ID relace je povinné'], 400);
        return;
    }

    // Validace ID relace
    $sessionId = validateInt($sessionId);

    if (!$sessionId) {
        sendResponse(['error' => 'Neplatné ID relace'], 400);
        return;
    }

    // Kontrola, zda relace existuje
    $pdo = getDbConnection();

    $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = :id");
    $stmt->execute(['id' => $sessionId]);

    $session = $stmt->fetch();

    if (!$session) {
        sendResponse(['error' => 'Inventurní relace nenalezena'], 404);
        return;
    }

    // Získání aktuálního uživatele
    $user = getCurrentUser();

    // Kontrola oprávnění - běžný uživatel může vidět všechny aktivní inventury, ale pouze své dokončené a zrušené
    if (!isAdminOrManager() && $session['status'] !== 'active' && $session['user_id'] != $user['id']) {
        error_log("getEntries - nedostatečná oprávnění, user_id: " . $user['id'] . ", session user_id: " . $session['user_id'] . ", status: " . $session['status']);
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    // Získání inventurních záznamů
    // Pro všechny uživatele (včetně administrátorů a manažerů) zobrazíme pouze jejich záznamy
    $sql = "
        SELECT
            ie.id,
            ie.product_id,
            ie.ean_code,
            p.name AS product_name,
            c.name AS category,
            p.pricebuy,
            t.rate AS tax_rate,
            p.pricesell,
            COALESCE(s.units, 0) AS current_stock,
            ie.zadane_mnozstvi,
            (ie.zadane_mnozstvi - COALESCE(s.units, 0)) AS difference,
            u.username AS user,
            u.id AS user_id,
            ie.last_updated
        FROM
            inventory_entries ie
        JOIN
            products p ON ie.product_id = p.id
        LEFT JOIN
            categories c ON p.category = c.id
        LEFT JOIN
            taxes t ON p.taxcat = t.id
        LEFT JOIN
            stockcurrent s ON p.id = s.product
        JOIN
            inventory_users u ON ie.user_id = u.id
        WHERE
            ie.session_id = :session_id
            AND ie.status = 'active'
            AND ie.user_id = :user_id
        ORDER BY
            ie.last_updated DESC
    ";

    $params = [
        'session_id' => $sessionId,
        'user_id' => $user['id']
    ];

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);

    $entries = $stmt->fetchAll();

    // Příprava dat pro odpověď
    $responseData = [];

    foreach ($entries as $entry) {
        // Pro všechny uživatele (včetně administrátorů a manažerů)
        $responseData[] = [
            'id' => $entry['id'],
            'product_id' => $entry['product_id'],
            'ean_code' => $entry['ean_code'],
            'product_name' => $entry['product_name'],
            'category' => $entry['category'],
            'pricebuy' => $entry['pricebuy'],
            'tax_rate' => $entry['tax_rate'],
            'pricesell' => $entry['pricesell'],
            'price_with_tax' => $entry['pricesell'] * (1 + $entry['tax_rate']),
            'current_stock' => $entry['current_stock'],
            'zadane_mnozstvi' => $entry['zadane_mnozstvi'],
            'difference' => $entry['difference'],
            'user' => $entry['user'],
            'user_id' => $entry['user_id'],
            'last_updated' => $entry['last_updated'],
            'can_edit' => true
        ];
    }

    sendResponse(['entries' => $responseData]);
}

/**
 * Vytvoření nového inventurního záznamu
 */
function createEntry() {
    // Získání JSON vstupu
    $jsonInput = file_get_contents('php://input');
    error_log("createEntry - JSON vstup: " . $jsonInput);

    $input = json_decode($jsonInput, true);

    if (!$input) {
        error_log("createEntry - Neplatný JSON: " . json_last_error_msg());
        sendResponse(['error' => 'Neplatný JSON: ' . json_last_error_msg()], 400);
        return;
    }

    error_log("createEntry - Dekódovaný vstup: " . print_r($input, true));

    // Validace vstupu
    $schema = [
        'session_id' => ['type' => 'int', 'required' => true],
        'ean_code' => ['type' => 'ean', 'required' => true],
        'zadane_mnozstvi' => ['type' => 'float', 'required' => true, 'min' => 0]
    ];

    error_log("createEntry - Validace vstupu podle schématu: " . print_r($schema, true));

    // Ruční validace vstupu
    $errors = [];
    $validData = [];

    // Validace session_id
    if (!isset($input['session_id'])) {
        $errors['session_id'] = 'This field is required';
    } else {
        $sessionId = filter_var($input['session_id'], FILTER_VALIDATE_INT);
        if ($sessionId === false) {
            $errors['session_id'] = 'Invalid integer value';
        } else {
            $validData['session_id'] = $sessionId;
        }
    }

    // Validace ean_code
    if (!isset($input['ean_code']) || $input['ean_code'] === '') {
        $errors['ean_code'] = 'This field is required';
    } else {
        $eanCode = trim($input['ean_code']);
        if (!preg_match('/^[0-9]+$/', $eanCode)) {
            $errors['ean_code'] = 'Invalid EAN code';
        } else {
            $validData['ean_code'] = $eanCode;
        }
    }

    // Validace zadane_mnozstvi
    if (!isset($input['zadane_mnozstvi'])) {
        $errors['zadane_mnozstvi'] = 'This field is required';
    } else {
        $zadaneMnozstvi = $input['zadane_mnozstvi'];
        if (is_string($zadaneMnozstvi)) {
            $zadaneMnozstvi = str_replace(',', '.', $zadaneMnozstvi);
        }
        $zadaneMnozstvi = filter_var($zadaneMnozstvi, FILTER_VALIDATE_FLOAT);
        if ($zadaneMnozstvi === false || $zadaneMnozstvi < 0) {
            $errors['zadane_mnozstvi'] = 'Invalid float value';
        } else {
            $validData['zadane_mnozstvi'] = $zadaneMnozstvi;
        }
    }

    error_log("createEntry - Výsledek ruční validace: " . (empty($errors) ? "OK" : "Chyby: " . print_r($errors, true)));

    if (!empty($errors)) {
        error_log("createEntry - Validace selhala: " . print_r($errors, true));
        sendResponse(['error' => 'Validace selhala', 'details' => $errors], 400);
        return;
    }

    // Získání hodnot
    $sessionId = $validData['session_id'];
    $eanCode = $validData['ean_code'];
    $zadaneMnozstvi = $validData['zadane_mnozstvi'];

    error_log("createEntry - Získané hodnoty: sessionId=" . $sessionId . ", eanCode=" . $eanCode . ", zadaneMnozstvi=" . $zadaneMnozstvi);

    // Kontrola, zda relace existuje
    $pdo = getDbConnection();
    error_log("createEntry - Připojení k databázi vytvořeno");

    $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = :id");
    error_log("createEntry - SQL dotaz pro kontrolu relace: SELECT * FROM inventory_sessions WHERE id = " . $sessionId);
    $stmt->execute(['id' => $sessionId]);

    $session = $stmt->fetch();
    error_log("createEntry - Výsledek kontroly relace: " . ($session ? "nalezena" : "nenalezena"));

    if (!$session) {
        error_log("createEntry - Inventurní relace nenalezena: " . $sessionId);
        sendResponse(['error' => 'Inventurní relace nenalezena'], 404);
        return;
    }

    // Kontrola, zda je relace aktivní
    error_log("createEntry - Status relace: " . $session['status']);
    if ($session['status'] !== 'active') {
        error_log("createEntry - Inventurní relace není aktivní: " . $session['status']);
        sendResponse(['error' => 'Inventurní relace není aktivní'], 400);
        return;
    }

    // Získání aktuálního uživatele
    $user = getCurrentUser();
    error_log("createEntry - Aktuální uživatel: " . print_r($user, true));

    // Kontrola, zda produkt existuje
    $sql = "
        SELECT p.*, COALESCE(s.units, 0) AS current_stock
        FROM products p
        LEFT JOIN stockcurrent s ON p.id = s.product
        WHERE p.code = :ean_code
    ";
    error_log("createEntry - SQL dotaz pro kontrolu produktu: " . $sql . " (ean_code=" . $eanCode . ")");

    $stmt = $pdo->prepare($sql);
    $stmt->execute(['ean_code' => $eanCode]);

    $product = $stmt->fetch();
    error_log("createEntry - Výsledek kontroly produktu: " . ($product ? "nalezen" : "nenalezen"));

    if (!$product) {
        error_log("createEntry - Produkt nebyl nalezen: " . $eanCode);
        sendResponse(['error' => 'Produkt nebyl nalezen'], 404);
        return;
    }

    error_log("createEntry - Nalezený produkt: " . print_r($product, true));

    // Získání aktuálního stavu skladu
    $currentStock = $product['current_stock'] ?? 0;
    error_log("createEntry - Aktuální stav skladu: " . $currentStock);

    try {
        // Začátek transakce
        $pdo->beginTransaction();
        error_log("createEntry - Transakce zahájena");

        // Kontrola, zda již existuje záznam pro tento produkt a relaci
        $stmt = $pdo->prepare("
            SELECT id
            FROM inventory_entries
            WHERE session_id = :session_id
            AND product_id = :product_id
            AND user_id = :user_id
            AND status = 'active'
        ");

        $stmt->execute([
            'session_id' => $sessionId,
            'product_id' => $product['id'],
            'user_id' => $user['id']
        ]);

        $existingEntry = $stmt->fetch();

        if ($existingEntry) {
            // Aktualizace existujícího záznamu
            $stmt = $pdo->prepare("
                UPDATE inventory_entries
                SET zadane_mnozstvi = :zadane_mnozstvi,
                    last_updated = NOW()
                WHERE id = :id
            ");

            $stmt->execute([
                'id' => $existingEntry['id'],
                'zadane_mnozstvi' => $zadaneMnozstvi
            ]);

            $entryId = $existingEntry['id'];
            error_log("createEntry - Existující záznam byl aktualizován (ID: " . $entryId . ")");
        } else {
            // Vytvoření nového inventurního záznamu
            $stmt = $pdo->prepare("
                INSERT INTO inventory_entries (
                    session_id,
                    product_id,
                    ean_code,
                    user_id,
                    zadane_mnozstvi
                )
                VALUES (
                    :session_id,
                    :product_id,
                    :ean_code,
                    :user_id,
                    :zadane_mnozstvi
                )
            ");

            $stmt->execute([
                'session_id' => $sessionId,
                'product_id' => $product['id'],
                'ean_code' => $eanCode,
                'user_id' => $user['id'],
                'zadane_mnozstvi' => $zadaneMnozstvi
            ]);

            $entryId = $pdo->lastInsertId();
            error_log("createEntry - Nový záznam byl vytvořen (ID: " . $entryId . ")");
        }

        // Aktualizace celkového zadaného množství v inventory_totals
        $stmt = $pdo->prepare("
            SELECT id, total_zadane_mnozstvi
            FROM inventory_totals
            WHERE session_id = :session_id
            AND product_id = :product_id
        ");

        $stmt->execute([
            'session_id' => $sessionId,
            'product_id' => $product['id']
        ]);

        $existingTotal = $stmt->fetch();

        if ($existingTotal) {
            error_log("createEntry - Existující záznam v inventory_totals nalezen (ID: " . $existingTotal['id'] . ")");

            // Získání všech záznamů pro tento produkt a relaci
            $stmt = $pdo->prepare("
                SELECT SUM(zadane_mnozstvi) AS total_zadane_mnozstvi
                FROM inventory_entries
                WHERE session_id = :session_id
                AND product_id = :product_id
                AND status = 'active'
            ");

            $stmt->execute([
                'session_id' => $sessionId,
                'product_id' => $product['id']
            ]);

            $totalResult = $stmt->fetch();
            $totalZadaneMnozstvi = $totalResult['total_zadane_mnozstvi'] ?? 0;

            error_log("createEntry - Celkové zadané množství: " . $totalZadaneMnozstvi);

            // Aktualizace existujícího záznamu
            $stmt = $pdo->prepare("
                UPDATE inventory_totals
                SET total_zadane_mnozstvi = :total_zadane_mnozstvi,
                    last_updated = NOW()
                WHERE id = :id
            ");

            $stmt->execute([
                'id' => $existingTotal['id'],
                'total_zadane_mnozstvi' => $totalZadaneMnozstvi
            ]);

            error_log("createEntry - Záznam v inventory_totals byl aktualizován");
        } else {
            error_log("createEntry - Vytváření nového záznamu v inventory_totals");

            // Vytvoření nového záznamu
            $stmt = $pdo->prepare("
                INSERT INTO inventory_totals (
                    session_id,
                    product_id,
                    total_zadane_mnozstvi
                )
                VALUES (
                    :session_id,
                    :product_id,
                    :total_zadane_mnozstvi
                )
            ");

            $stmt->execute([
                'session_id' => $sessionId,
                'product_id' => $product['id'],
                'total_zadane_mnozstvi' => $zadaneMnozstvi
            ]);

            error_log("createEntry - Nový záznam v inventory_totals byl vytvořen");
        }

        // Sledování změn stavu zásob
        $stmt = $pdo->prepare("
            INSERT INTO inventory_stock_changes (
                session_id,
                product_id,
                initial_stock,
                stock_changes_during_inventory
            )
            VALUES (
                :session_id,
                :product_id,
                :initial_stock,
                0
            )
            ON DUPLICATE KEY UPDATE
                initial_stock = :initial_stock
        ");

        $stmt->execute([
            'session_id' => $sessionId,
            'product_id' => $product['id'],
            'initial_stock' => $currentStock
        ]);

        error_log("createEntry - Záznam v inventory_stock_changes byl vytvořen/aktualizován");

        // Commit transakce
        $pdo->commit();
        error_log("createEntry - Transakce úspěšně dokončena");

        // Příprava odpovědi
        $response = [
            'success' => true,
            'entry_id' => $entryId,
            'message' => 'Inventurní záznam byl úspěšně ' . ($existingEntry ? 'aktualizován' : 'vytvořen'),
            'product' => [
                'id' => $product['id'],
                'ean_code' => $product['code'],
                'name' => $product['name'],
                'current_stock' => $currentStock,
                'zadane_mnozstvi' => $zadaneMnozstvi,
                'difference' => $zadaneMnozstvi - $currentStock
            ]
        ];

        error_log("createEntry - Odpověď: " . print_r($response, true));

        sendResponse($response);
    } catch (Exception $e) {
        // Rollback transakce v případě chyby
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
            error_log("createEntry - Transakce vrácena zpět");
        }

        error_log("createEntry - Chyba při vytváření inventurního záznamu: " . $e->getMessage());
        error_log("createEntry - Stack trace: " . $e->getTraceAsString());
        sendResponse(['error' => 'Došlo k chybě při vytváření inventurního záznamu: ' . $e->getMessage()], 500);
    }
}

/**
 * Získání detailů inventurního záznamu
 */
function getEntry() {
    // Získání ID záznamu
    $entryId = $_GET['id'] ?? null;

    if (!$entryId) {
        sendResponse(['error' => 'ID záznamu je povinné'], 400);
        return;
    }

    // Validace ID záznamu
    $entryId = validateInt($entryId);

    if (!$entryId) {
        sendResponse(['error' => 'Neplatné ID záznamu'], 400);
        return;
    }

    $pdo = getDbConnection();

    // Získání detailů záznamu
    $stmt = $pdo->prepare("
        SELECT
            ie.id,
            ie.session_id,
            ie.product_id,
            ie.ean_code,
            p.name AS product_name,
            c.name AS category,
            p.pricebuy,
            t.rate AS tax_rate,
            p.pricesell,
            COALESCE(s.units, 0) AS current_stock,
            ie.zadane_mnozstvi,
            (ie.zadane_mnozstvi - COALESCE(s.units, 0)) AS difference,
            u.username AS user,
            u.id AS user_id,
            ie.last_updated,
            ie.status
        FROM
            inventory_entries ie
        JOIN
            products p ON ie.product_id = p.id
        LEFT JOIN
            categories c ON p.category = c.id
        LEFT JOIN
            taxes t ON p.taxcat = t.id
        LEFT JOIN
            stockcurrent s ON p.id = s.product
        JOIN
            inventory_users u ON ie.user_id = u.id
        WHERE
            ie.id = :id
    ");

    $stmt->execute(['id' => $entryId]);

    $entry = $stmt->fetch();

    if (!$entry) {
        sendResponse(['error' => 'Inventurní záznam nenalezen'], 404);
        return;
    }

    // Získání aktuálního uživatele
    $user = getCurrentUser();

    // Kontrola oprávnění - běžný uživatel může vidět pouze své záznamy
    if (!isAdminOrManager() && $entry['user_id'] != $user['id']) {
        error_log("getEntry - nedostatečná oprávnění, user_id: " . $user['id'] . ", entry user_id: " . $entry['user_id']);
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    // Příprava odpovědi
    $response = [
        'id' => $entry['id'],
        'session_id' => $entry['session_id'],
        'product_id' => $entry['product_id'],
        'ean_code' => $entry['ean_code'],
        'product_name' => $entry['product_name'],
        'category' => $entry['category'],
        'pricebuy' => $entry['pricebuy'],
        'tax_rate' => $entry['tax_rate'],
        'pricesell' => $entry['pricesell'],
        'price_with_tax' => $entry['pricesell'] * (1 + $entry['tax_rate']),
        'current_stock' => $entry['current_stock'],
        'zadane_mnozstvi' => $entry['zadane_mnozstvi'],
        'difference' => $entry['difference'],
        'user' => $entry['user'],
        'user_id' => $entry['user_id'],
        'last_updated' => $entry['last_updated'],
        'status' => $entry['status'],
        'can_edit' => isAdminOrManager() || $entry['user_id'] === $user['id']
    ];

    sendResponse(['entry' => $response]);
}

/**
 * Aktualizace inventurního záznamu
 */
function updateEntry() {
    // Získání JSON vstupu
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        sendResponse(['error' => 'Neplatný JSON'], 400);
        return;
    }

    // Validace vstupu
    $schema = [
        'id' => ['type' => 'int', 'required' => true],
        'zadane_mnozstvi' => ['type' => 'float', 'required' => true, 'min' => 0]
    ];

    $validation = validateData($input, $schema);

    if (!$validation['valid']) {
        sendResponse(['error' => 'Validace selhala', 'details' => $validation['errors']], 400);
        return;
    }

    $entryId = $validation['data']['id'];
    $zadaneMnozstvi = $validation['data']['zadane_mnozstvi'];

    $pdo = getDbConnection();

    // Kontrola, zda záznam existuje
    $stmt = $pdo->prepare("
        SELECT
            ie.*,
            s.status AS session_status
        FROM
            inventory_entries ie
        JOIN
            inventory_sessions s ON ie.session_id = s.id
        WHERE
            ie.id = :id
    ");

    $stmt->execute(['id' => $entryId]);

    $entry = $stmt->fetch();

    if (!$entry) {
        sendResponse(['error' => 'Inventurní záznam nenalezen'], 404);
        return;
    }

    // Kontrola, zda je relace aktivní
    if ($entry['session_status'] !== 'active') {
        sendResponse(['error' => 'Inventurní relace není aktivní'], 400);
        return;
    }

    // Získání aktuálního uživatele
    $user = getCurrentUser();

    // Kontrola oprávnění - běžný uživatel může upravovat pouze své záznamy
    if (!isAdminOrManager() && $entry['user_id'] != $user['id']) {
        error_log("updateEntry - nedostatečná oprávnění, user_id: " . $user['id'] . ", entry user_id: " . $entry['user_id']);
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    try {
        // Aktualizace záznamu
        $stmt = $pdo->prepare("
            UPDATE inventory_entries
            SET zadane_mnozstvi = :zadane_mnozstvi
            WHERE id = :id
        ");

        $stmt->execute([
            'id' => $entryId,
            'zadane_mnozstvi' => $zadaneMnozstvi
        ]);

        // Získání aktuálního stavu skladu
        $stmt = $pdo->prepare("
            SELECT COALESCE(units, 0) AS current_stock
            FROM stockcurrent
            WHERE product = :product_id
        ");

        $stmt->execute(['product_id' => $entry['product_id']]);

        $stockResult = $stmt->fetch();
        $currentStock = $stockResult ? $stockResult['current_stock'] : 0;

        sendResponse([
            'success' => true,
            'message' => 'Inventurní záznam byl úspěšně aktualizován',
            'current_stock' => $currentStock,
            'zadane_mnozstvi' => $zadaneMnozstvi,
            'difference' => $zadaneMnozstvi - $currentStock
        ]);
    } catch (Exception $e) {
        error_log("Chyba při aktualizaci inventurního záznamu: " . $e->getMessage());
        sendResponse(['error' => 'Došlo k chybě při aktualizaci inventurního záznamu'], 500);
    }
}

/**
 * Smazání inventurního záznamu
 */
function deleteEntry() {
    global $pathParts, $method;

    error_log("deleteEntry - začátek funkce");
    error_log("deleteEntry - HTTP metoda: $method");
    error_log("deleteEntry - PATH_INFO: " . ($_SERVER['PATH_INFO'] ?? '/'));
    error_log("deleteEntry - GET parametry: " . print_r($_GET, true));

    // Získání ID záznamu z URL, GET parametru nebo JSON vstupu
    $entryId = null;

    error_log("deleteEntry - URL: " . $_SERVER['REQUEST_URI']);
    error_log("deleteEntry - GET parametry: " . print_r($_GET, true));
    error_log("deleteEntry - POST data: " . file_get_contents('php://input'));

    // Pokud je ID v URL (např. /inventory/entries/123)
    if (isset($pathParts[2]) && is_numeric($pathParts[2])) {
        $entryId = $pathParts[2];
        error_log("deleteEntry - ID získáno z URL: $entryId");
    }
    // Pokud je ID v GET parametru (např. ?id=123)
    else if (isset($_GET['id'])) {
        $entryId = $_GET['id'];
        error_log("deleteEntry - ID získáno z GET parametru: $entryId");
    }
    // Pokud je ID v JSON vstupu (např. v těle POST požadavku)
    else {
        $input = json_decode(file_get_contents('php://input'), true);
        error_log("deleteEntry - JSON vstup: " . print_r($input, true));
        if ($input && isset($input['id'])) {
            $entryId = $input['id'];
            error_log("deleteEntry - ID získáno z JSON vstupu: $entryId");
        }
    }

    if (!$entryId) {
        error_log("deleteEntry - chybí ID záznamu");
        sendResponse(['error' => 'ID záznamu je povinné'], 400);
        return;
    }

    // Validace ID záznamu
    $entryId = validateInt($entryId);

    if (!$entryId) {
        error_log("deleteEntry - neplatné ID záznamu");
        sendResponse(['error' => 'Neplatné ID záznamu'], 400);
        return;
    }

    error_log("deleteEntry - validované ID záznamu: $entryId");

    $pdo = getDbConnection();

    // Kontrola, zda záznam existuje
    $stmt = $pdo->prepare("
        SELECT
            ie.*,
            s.status AS session_status
        FROM
            inventory_entries ie
        JOIN
            inventory_sessions s ON ie.session_id = s.id
        WHERE
            ie.id = :id
    ");

    $stmt->execute(['id' => $entryId]);

    $entry = $stmt->fetch();

    if (!$entry) {
        error_log("deleteEntry - záznam s ID $entryId nebyl nalezen");
        sendResponse(['error' => 'Inventurní záznam nenalezen'], 404);
        return;
    }

    error_log("deleteEntry - záznam nalezen, session_status: " . $entry['session_status']);

    // Kontrola, zda je relace aktivní
    if ($entry['session_status'] !== 'active') {
        error_log("deleteEntry - relace není aktivní, status: " . $entry['session_status']);
        sendResponse(['error' => 'Inventurní relace není aktivní'], 400);
        return;
    }

    error_log("deleteEntry - relace je aktivní, pokračuji");

    // Získání aktuálního uživatele
    $user = getCurrentUser();

    // Kontrola oprávnění - běžný uživatel může mazat pouze své záznamy
    if (!isAdminOrManager() && $entry['user_id'] != $user['id']) {
        error_log("deleteEntry - nedostatečná oprávnění, user_id: " . $user['id'] . ", entry user_id: " . $entry['user_id']);
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    try {
        // Začátek transakce
        $pdo->beginTransaction();

        // Získání informací o záznamu před smazáním pro aktualizaci celkových součtů
        $infoStmt = $pdo->prepare("
            SELECT product_id, session_id, zadane_mnozstvi
            FROM inventory_entries
            WHERE id = :id
        ");
        $infoStmt->execute(['id' => $entryId]);
        $entryInfo = $infoStmt->fetch();

        if (!$entryInfo) {
            $pdo->rollBack();
            error_log("Záznam s ID $entryId nebyl nalezen");
            sendResponse(['error' => 'Záznam nebyl nalezen'], 404);
            return;
        }

        // Fyzické smazání záznamu z databáze
        $stmt = $pdo->prepare("
            DELETE FROM inventory_entries
            WHERE id = :id
        ");

        $stmt->execute(['id' => $entryId]);

        // Kontrola, zda byl záznam skutečně smazán
        if ($stmt->rowCount() > 0) {
            // Aktualizace celkových součtů v tabulce inventory_totals
            $updateTotalsStmt = $pdo->prepare("
                UPDATE inventory_totals
                SET total_zadane_mnozstvi = (
                    SELECT COALESCE(SUM(zadane_mnozstvi), 0)
                    FROM inventory_entries
                    WHERE product_id = :product_id
                    AND session_id = :session_id
                    AND status = 'active'
                )
                WHERE product_id = :product_id
                AND session_id = :session_id
            ");

            error_log("deleteEntry - aktualizace celkových součtů, product_id: " . $entryInfo['product_id'] . ", session_id: " . $entryInfo['session_id']);

            try {
                $updateTotalsStmt->execute([
                    'product_id' => $entryInfo['product_id'],
                    'session_id' => $entryInfo['session_id']
                ]);
                error_log("deleteEntry - aktualizace celkových součtů proběhla úspěšně");
            } catch (Exception $e) {
                error_log("deleteEntry - chyba při aktualizaci celkových součtů: " . $e->getMessage());
                error_log("deleteEntry - SQL dotaz: " . $updateTotalsStmt->queryString);
                error_log("deleteEntry - parametry: product_id=" . $entryInfo['product_id'] . ", session_id=" . $entryInfo['session_id']);
                throw $e;
            }

            // Pokud není žádný záznam, odstraníme záznam z inventory_totals
            $deleteTotalsStmt = $pdo->prepare("
                DELETE FROM inventory_totals
                WHERE product_id = :product_id
                AND session_id = :session_id
                AND total_zadane_mnozstvi = 0
            ");

            error_log("deleteEntry - odstranění prázdných záznamů z inventory_totals, product_id: " . $entryInfo['product_id'] . ", session_id: " . $entryInfo['session_id']);

            try {
                $deleteTotalsStmt->execute([
                    'product_id' => $entryInfo['product_id'],
                    'session_id' => $entryInfo['session_id']
                ]);
                error_log("deleteEntry - odstranění prázdných záznamů proběhlo úspěšně");
            } catch (Exception $e) {
                error_log("deleteEntry - chyba při odstraňování prázdných záznamů: " . $e->getMessage());
                error_log("deleteEntry - SQL dotaz: " . $deleteTotalsStmt->queryString);
                error_log("deleteEntry - parametry: product_id=" . $entryInfo['product_id'] . ", session_id=" . $entryInfo['session_id']);
                throw $e;
            }

            // Commit transakce
            $pdo->commit();

            sendResponse([
                'success' => true,
                'message' => 'Inventurní záznam byl úspěšně smazán'
            ]);
        } else {
            $pdo->rollBack();
            error_log("Záznam s ID $entryId nemohl být smazán");
            sendResponse(['error' => 'Záznam nemohl být smazán'], 500);
        }
    } catch (Exception $e) {
        // Rollback transakce v případě chyby
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Chyba při mazání inventurního záznamu: " . $e->getMessage());
        sendResponse(['error' => 'Došlo k chybě při mazání inventurního záznamu: ' . $e->getMessage()], 500);
    }
}

/**
 * Zpracování požadavku na inventuru
 */
function handleInventory() {
    global $method;

    if ($method !== 'GET') {
        sendResponse(['error' => 'Metoda není povolena'], 405);
        return;
    }

    // Získání ID relace
    $sessionId = $_GET['session_id'] ?? null;

    if (!$sessionId) {
        sendResponse(['error' => 'ID relace je povinné'], 400);
        return;
    }

    // Validace ID relace
    $sessionId = validateInt($sessionId);

    if (!$sessionId) {
        sendResponse(['error' => 'Neplatné ID relace'], 400);
        return;
    }

    // Kontrola, zda relace existuje
    $pdo = getDbConnection();

    $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = :id");
    $stmt->execute(['id' => $sessionId]);

    $session = $stmt->fetch();

    if (!$session) {
        sendResponse(['error' => 'Inventurní relace nenalezena'], 404);
        return;
    }

    // Získání aktuálního uživatele
    $user = getCurrentUser();

    // Kontrola oprávnění - běžný uživatel může vidět všechny aktivní inventury, ale pouze své dokončené a zrušené
    if (!isAdminOrManager() && $session['status'] !== 'active' && $session['user_id'] != $user['id']) {
        error_log("handleInventory - nedostatečná oprávnění, user_id: " . $user['id'] . ", session user_id: " . $session['user_id'] . ", status: " . $session['status']);
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    // Získání inventurních záznamů - sčítání záznamů od všech uživatelů
    $stmt = $pdo->prepare("
        SELECT
            p.id AS product_id,
            p.code AS ean_code,
            p.name AS product_name,
            c.name AS category,
            p.pricebuy,
            t.rate AS tax_rate,
            p.pricesell,
            COALESCE(s.units, 0) AS current_stock,
            SUM(ie.zadane_mnozstvi) AS zadane_mnozstvi,
            (SUM(ie.zadane_mnozstvi) - COALESCE(s.units, 0)) AS difference,
            GROUP_CONCAT(DISTINCT u.username) AS users,
            MAX(ie.last_updated) AS last_updated
        FROM
            inventory_entries ie
        JOIN
            products p ON ie.product_id = p.id
        LEFT JOIN
            categories c ON p.category = c.id
        LEFT JOIN
            taxes t ON p.taxcat = t.id
        LEFT JOIN
            stockcurrent s ON p.id = s.product
        JOIN
            inventory_users u ON ie.user_id = u.id
        WHERE
            ie.session_id = :session_id
            AND ie.status = 'active'
        GROUP BY
            p.id, p.code, p.name, c.name, p.pricebuy, t.rate, p.pricesell, s.units
        ORDER BY
            p.name
    ");

    $stmt->execute(['session_id' => $sessionId]);

    $inventory = $stmt->fetchAll();

    sendResponse(['inventory' => $inventory]);
}

/**
 * Zpracování požadavků na celkovou inventuru
 */
function handleTotalEntries() {
    global $method;

    switch ($method) {
        case 'GET':
            // Získání seznamu celkových inventurních záznamů
            getTotalEntries();
            break;

        default:
            sendResponse(['error' => 'Metoda není povolena'], 405);
            break;
    }
}

/**
 * Získání seznamu celkových inventurních záznamů
 */
function getTotalEntries() {
    // Získání ID relace
    $sessionId = $_GET['session_id'] ?? null;

    if (!$sessionId) {
        sendResponse(['error' => 'ID relace je povinné'], 400);
        return;
    }

    // Validace ID relace
    $sessionId = validateInt($sessionId);

    if (!$sessionId) {
        sendResponse(['error' => 'Neplatné ID relace'], 400);
        return;
    }

    // Kontrola, zda relace existuje
    $pdo = getDbConnection();

    $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = :id");
    $stmt->execute(['id' => $sessionId]);

    $session = $stmt->fetch();

    if (!$session) {
        sendResponse(['error' => 'Inventurní relace nenalezena'], 404);
        return;
    }

    // Získání aktuálního uživatele
    $user = getCurrentUser();

    // Kontrola oprávnění - běžný uživatel může vidět všechny aktivní inventury, ale pouze své dokončené a zrušené
    if (!isAdminOrManager() && $session['status'] !== 'active' && $session['user_id'] != $user['id']) {
        error_log("getTotalEntries - nedostatečná oprávnění, user_id: " . $user['id'] . ", session user_id: " . $session['user_id'] . ", status: " . $session['status']);
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    // Získání celkových inventurních záznamů z tabulky inventory_totals
    $sql = "
        SELECT
            it.id,
            it.product_id,
            p.code AS ean_code,
            p.name AS product_name,
            c.name AS category,
            p.pricebuy,
            t.rate AS tax_rate,
            p.pricesell,
            COALESCE(s.units, 0) AS current_stock,
            it.total_zadane_mnozstvi AS zadane_mnozstvi,
            (it.total_zadane_mnozstvi - COALESCE(s.units, 0)) AS difference,
            (SELECT GROUP_CONCAT(DISTINCT u.username)
             FROM inventory_entries ie
             JOIN inventory_users u ON ie.user_id = u.id
             WHERE ie.session_id = it.session_id
             AND ie.product_id = it.product_id
             AND ie.status = 'active') AS users,
            it.last_updated
        FROM
            inventory_totals it
        JOIN
            products p ON it.product_id = p.id
        LEFT JOIN
            categories c ON p.category = c.id
        LEFT JOIN
            taxes t ON p.taxcat = t.id
        LEFT JOIN
            stockcurrent s ON p.id = s.product
        WHERE
            it.session_id = :session_id
        ORDER BY
            p.name
    ";

    $params = ['session_id' => $sessionId];

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);

    $entries = $stmt->fetchAll();

    // Příprava dat pro odpověď
    $responseData = [];

    foreach ($entries as $entry) {
        $responseData[] = [
            'id' => $entry['id'],
            'product_id' => $entry['product_id'],
            'ean_code' => $entry['ean_code'],
            'product_name' => $entry['product_name'],
            'category' => $entry['category'],
            'pricebuy' => $entry['pricebuy'],
            'tax_rate' => $entry['tax_rate'],
            'pricesell' => $entry['pricesell'],
            'price_with_tax' => $entry['pricesell'] * (1 + $entry['tax_rate']),
            'current_stock' => $entry['current_stock'],
            'zadane_mnozstvi' => $entry['zadane_mnozstvi'],
            'difference' => $entry['difference'],
            'users' => $entry['users'],
            'last_updated' => $entry['last_updated'],
            'can_edit' => true
        ];
    }

    sendResponse(['entries' => $responseData]);
}

/**
 * Zpracování požadavků na chybějící produkty v inventuře
 */
function handleMissingProducts() {
    global $method;

    switch ($method) {
        case 'GET':
            // Získání seznamu produktů, které nejsou v inventuře
            getMissingProducts();
            break;

        default:
            sendResponse(['error' => 'Metoda není povolena'], 405);
            break;
    }
}

/**
 * Získání seznamu produktů, které nejsou v inventuře
 */
function getMissingProducts() {
    error_log("getMissingProducts - začátek funkce");

    // Získání ID relace
    $sessionId = $_GET['session_id'] ?? null;
    error_log("getMissingProducts - session_id: " . $sessionId);

    if (!$sessionId) {
        sendResponse(['error' => 'ID relace je povinné'], 400);
        return;
    }

    // Validace ID relace
    $sessionId = validateInt($sessionId);

    if (!$sessionId) {
        sendResponse(['error' => 'Neplatné ID relace'], 400);
        return;
    }

    // Kontrola, zda relace existuje
    $pdo = getDbConnection();

    $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = :id");
    $stmt->execute(['id' => $sessionId]);

    $session = $stmt->fetch();

    if (!$session) {
        sendResponse(['error' => 'Inventurní relace nenalezena'], 404);
        return;
    }

    // Získání aktuálního uživatele
    $user = getCurrentUser();

    // Kontrola oprávnění - běžný uživatel může vidět všechny aktivní inventury, ale pouze své dokončené a zrušené
    if (!isAdminOrManager() && $session['status'] !== 'active' && $session['user_id'] != $user['id']) {
        error_log("getMissingProducts - nedostatečná oprávnění, user_id: " . $user['id'] . ", session user_id: " . $session['user_id'] . ", status: " . $session['status']);
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    // Získání produktů, které nejsou v inventuře
    $sql = "
        SELECT
            p.id AS product_id,
            p.code AS ean_code,
            p.name AS product_name,
            c.name AS category,
            p.pricebuy,
            COALESCE(t.rate, 0) AS tax_rate,
            p.pricesell,
            COALESCE(s.units, 0) AS current_stock
        FROM
            products p
        LEFT JOIN
            categories c ON p.category = c.id
        LEFT JOIN
            taxes t ON p.taxcat = t.id
        LEFT JOIN
            stockcurrent s ON p.id = s.product
        LEFT JOIN (
            SELECT DISTINCT product_id
            FROM inventory_entries
            WHERE session_id = :session_id AND status = 'active'
        ) ie ON p.id = ie.product_id
        WHERE
            ie.product_id IS NULL
        ORDER BY
            p.name
    ";

    $params = ['session_id' => $sessionId];

    error_log("getMissingProducts - SQL dotaz: " . $sql);
    error_log("getMissingProducts - parametry: " . print_r($params, true));

    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        $products = $stmt->fetchAll();
        error_log("getMissingProducts - počet nalezených produktů: " . count($products));
    } catch (Exception $e) {
        error_log("getMissingProducts - chyba při provádění SQL dotazu: " . $e->getMessage());
        error_log("getMissingProducts - stack trace: " . $e->getTraceAsString());
        sendResponse(['error' => 'Došlo k chybě při získávání chybějících produktů: ' . $e->getMessage()], 500);
        return;
    }

    // Příprava dat pro odpověď
    $responseData = [];

    foreach ($products as $product) {
        $responseData[] = [
            'product_id' => $product['product_id'],
            'ean_code' => $product['ean_code'],
            'product_name' => $product['product_name'],
            'category' => $product['category'],
            'pricebuy' => $product['pricebuy'],
            'tax_rate' => $product['tax_rate'],
            'pricesell' => $product['pricesell'],
            'price_with_tax' => $product['pricesell'] * (1 + $product['tax_rate']),
            'current_stock' => $product['current_stock']
        ];
    }

    sendResponse(['products' => $responseData]);
}

/**
 * Zpracování požadavku na aktualizaci inventurních záznamů
 */
function handleRefresh() {
    global $method;

    error_log("handleRefresh - začátek funkce");

    if ($method !== 'POST') {
        error_log("handleRefresh - nepovolená metoda: " . $method);
        sendResponse(['error' => 'Metoda není povolena'], 405);
        return;
    }

    // Kontrola, zda je uživatel admin nebo manažer
    if (!isAdminOrManager()) {
        error_log("handleRefresh - nedostatečná oprávnění");
        sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
        return;
    }

    // Získání JSON vstupu
    $input = json_decode(file_get_contents('php://input'), true);
    error_log("handleRefresh - dekódovaný JSON vstup: " . print_r($input, true));

    if (!$input || !isset($input['session_id'])) {
        error_log("handleRefresh - neplatný JSON nebo chybějící session_id");
        sendResponse(['error' => 'Neplatný JSON nebo chybějící session_id'], 400);
        return;
    }

    // Získání ID relace přímo z vstupu
    $sessionId = intval($input['session_id']);
    error_log("handleRefresh - session_id: " . $sessionId);

    if ($sessionId <= 0) {
        error_log("handleRefresh - neplatné ID relace");
        sendResponse(['error' => 'Neplatné ID relace'], 400);
        return;
    }

    // Připojení k databázi
    $pdo = getDbConnection();

    try {
        // Kontrola, zda relace existuje a je aktivní
        $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = ? AND status = 'active'");
        $stmt->execute([$sessionId]);
        $session = $stmt->fetch();

        if (!$session) {
            error_log("handleRefresh - relace nenalezena nebo není aktivní");
            sendResponse(['error' => 'Inventurní relace nenalezena nebo není aktivní'], 404);
            return;
        }

        // Získání ID administrátora
        $adminStmt = $pdo->prepare("SELECT id FROM inventory_users WHERE role = 'admin' LIMIT 1");
        $adminStmt->execute();
        $adminId = $adminStmt->fetchColumn();

        if (!$adminId) {
            error_log("handleRefresh - nenalezen žádný administrátor");
            sendResponse(['error' => 'Nenalezen žádný administrátor'], 500);
            return;
        }

        // Získání aktuálních stavů zásob
        $stockStmt = $pdo->prepare("SELECT product, units FROM stockcurrent");
        $stockStmt->execute();
        $currentStock = [];
        while ($row = $stockStmt->fetch()) {
            $currentStock[$row['product']] = $row['units'];
        }

        // Získání předchozích stavů zásob
        $previousStockStmt = $pdo->prepare("
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'previous_stock'
        ");
        $previousStockStmt->execute();
        $previousStockTableExists = $previousStockStmt->fetchColumn() > 0;

        $previousStock = [];
        if ($previousStockTableExists) {
            $previousStockStmt = $pdo->prepare("SELECT product_id, units FROM previous_stock");
            $previousStockStmt->execute();
            while ($row = $previousStockStmt->fetch()) {
                $previousStock[$row['product_id']] = $row['units'];
            }
        } else {
            // Vytvoření tabulky previous_stock
            $pdo->exec("
                CREATE TABLE `previous_stock` (
                  `id` INT AUTO_INCREMENT PRIMARY KEY,
                  `product_id` VARCHAR(255) NOT NULL,
                  `units` DECIMAL(10,3) NOT NULL DEFAULT 0,
                  `last_updated` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  UNIQUE KEY `uk_previous_stock_product` (`product_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");

            // Naplnění tabulky previous_stock aktuálními stavy zásob
            foreach ($currentStock as $productId => $units) {
                $previousStock[$productId] = $units;
                $pdo->prepare("INSERT INTO previous_stock (product_id, units) VALUES (?, ?)")
                    ->execute([$productId, $units]);
            }
        }

        // Získání produktů v inventuře
        $productsStmt = $pdo->prepare("
            SELECT DISTINCT product_id
            FROM inventory_entries
            WHERE session_id = ? AND status = 'active'
        ");
        $productsStmt->execute([$sessionId]);
        $products = $productsStmt->fetchAll(PDO::FETCH_COLUMN);

        // Aktualizace zadaného množství pro každý produkt
        $updatedCount = 0;
        foreach ($products as $productId) {
            // Získání aktuálního stavu zásob
            $currentStockValue = isset($currentStock[$productId]) ? $currentStock[$productId] : 0;

            // Získání předchozího stavu zásob
            $previousStockValue = isset($previousStock[$productId]) ? $previousStock[$productId] : $currentStockValue;

            // Výpočet rozdílu mezi předchozím a aktuálním stavem zásob
            $stockDifference = $previousStockValue - $currentStockValue;

            // Aktualizujeme zadané množství pouze pokud došlo ke změně stavu zásob
            if ($stockDifference != 0) {
                // Získání předchozího zadaného množství
                $zadaneStmt = $pdo->prepare("
                    SELECT zadane_mnozstvi
                    FROM inventory_entries
                    WHERE product_id = ?
                    AND session_id = ?
                    AND status = 'active'
                    AND user_id = ?
                    LIMIT 1
                ");
                $zadaneStmt->execute([$productId, $sessionId, $adminId]);
                $previousZadaneMnozstvi = $zadaneStmt->fetchColumn();

                // Pokud předchozí zadané množství neexistuje, použijeme aktuální stav zásob
                if ($previousZadaneMnozstvi === false) {
                    $previousZadaneMnozstvi = $currentStockValue;
                }

                // Nové zadané množství = předchozí zadané množství - rozdíl
                $newZadaneMnozstvi = $previousZadaneMnozstvi - $stockDifference;

                // Zajistíme, že zadané množství nebude záporné
                if ($newZadaneMnozstvi < 0) {
                    $newZadaneMnozstvi = 0;
                }

                // Aktualizace zadaného množství
                $updateStmt = $pdo->prepare("
                    UPDATE inventory_entries
                    SET zadane_mnozstvi = ?
                    WHERE product_id = ?
                    AND session_id = ?
                    AND status = 'active'
                    AND user_id = ?
                ");
                $updateStmt->execute([$newZadaneMnozstvi, $productId, $sessionId, $adminId]);
                $rowCount = $updateStmt->rowCount();

                // Aktualizujeme pouze existující záznamy, nepřidáváme nové
                $updatedCount += $rowCount;
            }

            // Aktualizace předchozího stavu zásob
            $updatePreviousStockStmt = $pdo->prepare("
                INSERT INTO previous_stock (product_id, units)
                VALUES (?, ?)
                ON DUPLICATE KEY UPDATE units = ?
            ");
            $updatePreviousStockStmt->execute([$productId, $currentStockValue, $currentStockValue]);
        }

        error_log("handleRefresh - celkem aktualizováno záznamů: $updatedCount");
        sendResponse(['success' => true, 'message' => 'Inventurní záznamy byly úspěšně aktualizovány']);
    } catch (Exception $e) {
        error_log("Chyba při aktualizaci inventurních záznamů: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
        sendResponse(['error' => 'Došlo k chybě při aktualizaci inventurních záznamů: ' . $e->getMessage()], 500);
    }
}

/**
 * Odeslání JSON odpovědi
 *
 * @param mixed $data Data odpovědi
 * @param int $statusCode HTTP stavový kód
 */
function sendResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}

