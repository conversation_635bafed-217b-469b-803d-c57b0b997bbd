<?php
/**
 * <PERSON><PERSON><PERSON><PERSON> test z<PERSON><PERSON><PERSON><PERSON>
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='cs'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Rych<PERSON>ý test aplikace</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".ok { color: green; font-weight: bold; }";
echo ".error { color: red; font-weight: bold; }";
echo ".warning { color: orange; font-weight: bold; }";
echo ".test { margin: 10px 0; padding: 10px; border-left: 3px solid #ccc; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🚀 Rychlý test inventurní aplikace</h1>";

$tests_passed = 0;
$tests_total = 0;

// Test 1: Konfigurace databáze
$tests_total++;
echo "<div class='test'>";
echo "<h3>Test 1: Konfigurace databáze</h3>";
try {
    $config = require_once 'config/database.php';
    echo "<span class='ok'>✓ PROŠEL</span> - Konfigurace načtena";
    $tests_passed++;
} catch (Exception $e) {
    echo "<span class='error'>✗ SELHAL</span> - " . $e->getMessage();
}
echo "</div>";

// Test 2: Připojení k databázi
$tests_total++;
echo "<div class='test'>";
echo "<h3>Test 2: Připojení k databázi</h3>";
try {
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    echo "<span class='ok'>✓ PROŠEL</span> - Připojení úspěšné";
    $tests_passed++;
} catch (Exception $e) {
    echo "<span class='error'>✗ SELHAL</span> - " . $e->getMessage();
    echo "</body></html>";
    exit;
}
echo "</div>";

// Test 3: Základní tabulky
$tests_total++;
echo "<div class='test'>";
echo "<h3>Test 3: Základní tabulky UniCentaOPOS</h3>";
$required_tables = ['products', 'stockcurrent', 'people'];
$missing = [];

foreach ($required_tables as $table) {
    $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
    if ($stmt->rowCount() == 0) {
        $missing[] = $table;
    }
}

if (empty($missing)) {
    echo "<span class='ok'>✓ PROŠEL</span> - Všechny základní tabulky existují";
    $tests_passed++;
} else {
    echo "<span class='error'>✗ SELHAL</span> - Chybí: " . implode(', ', $missing);
}
echo "</div>";

// Test 4: API autentifikace
$tests_total++;
echo "<div class='test'>";
echo "<h3>Test 4: API autentifikace</h3>";
try {
    $url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/simple_auth.php?action=check";
    $context = stream_context_create(['http' => ['timeout' => 5]]);
    $response = @file_get_contents($url, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<span class='ok'>✓ PROŠEL</span> - API autentifikace odpovídá";
            $tests_passed++;
        } else {
            echo "<span class='warning'>⚠ ČÁSTEČNĚ</span> - API odpovídá, ale nevrací JSON";
        }
    } else {
        echo "<span class='error'>✗ SELHAL</span> - API neodpovídá";
    }
} catch (Exception $e) {
    echo "<span class='error'>✗ SELHAL</span> - " . $e->getMessage();
}
echo "</div>";

// Test 5: API produkty
$tests_total++;
echo "<div class='test'>";
echo "<h3>Test 5: API produkty</h3>";
try {
    $url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/products.php?action=search&ean=test";
    $context = stream_context_create(['http' => ['timeout' => 5]]);
    $response = @file_get_contents($url, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<span class='ok'>✓ PROŠEL</span> - API produkty odpovídá";
            $tests_passed++;
        } else {
            echo "<span class='warning'>⚠ ČÁSTEČNĚ</span> - API odpovídá, ale nevrací JSON";
        }
    } else {
        echo "<span class='error'>✗ SELHAL</span> - API neodpovídá";
    }
} catch (Exception $e) {
    echo "<span class='error'>✗ SELHAL</span> - " . $e->getMessage();
}
echo "</div>";

// Souhrn
echo "<hr>";
echo "<h2>📊 Souhrn testů</h2>";
echo "<p><strong>Úspěšnost:</strong> $tests_passed/$tests_total testů prošlo (" . round(($tests_passed/$tests_total)*100) . "%)</p>";

if ($tests_passed == $tests_total) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #155724; margin: 0;'>🎉 Všechny testy prošly!</h3>";
    echo "<p style='margin: 10px 0 0 0;'>Aplikace je připravena k použití.</p>";
    echo "</div>";
    
    echo "<h3>🚀 Další kroky:</h3>";
    echo "<ol>";
    echo "<li><a href='index.html' target='_blank'><strong>Otevřít aplikaci</strong></a></li>";
    echo "<li>Přihlásit se s údaji: <code>admin</code> / <code>admin123</code></li>";
    echo "<li>Vytvořit novou inventuru</li>";
    echo "<li>Otestovat přidávání produktů</li>";
    echo "</ol>";
    
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24; margin: 0;'>⚠️ Některé testy selhaly</h3>";
    echo "<p style='margin: 10px 0 0 0;'>Před použitím aplikace vyřešte problémy uvedené výše.</p>";
    echo "</div>";
    
    echo "<h3>🔧 Doporučené akce:</h3>";
    echo "<ul>";
    if ($tests_passed < 3) {
        echo "<li>Zkontrolujte připojení k databázi</li>";
        echo "<li>Ověřte, že databáze UniCentaOPOS existuje a obsahuje potřebné tabulky</li>";
    }
    if ($tests_passed >= 3 && $tests_passed < 5) {
        echo "<li>Zkontrolujte, zda webový server běží správně</li>";
        echo "<li>Ověřte, že API soubory jsou dostupné</li>";
    }
    echo "<li><a href='check_tables.php' target='_blank'>Spustit kontrolu a vytvoření tabulek</a></li>";
    echo "</ul>";
}

echo "<hr>";
echo "<p><em>Test dokončen: " . date('Y-m-d H:i:s') . "</em></p>";

echo "</body>";
echo "</html>";
?>
