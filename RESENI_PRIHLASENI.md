# Řešení problému s přihlášením do aplikace

## 🔍 Problém
Všechny testy prošly, ale při skutečném přihlášení do aplikace se nemůže připojit k databázi a tudíž se nemůže přihlásit.

## 🎯 Identifikovaná příčina
Problém byl v nekonzistentním ukládání hesla admin uživatele:
- Funkce `ensureDefaultAdminExists()` ukládala heslo v čistém textu
- Později byla změněna na hashované heslo
- Funkce `authenticateUser()` má složitou logiku pro ověřování různých typů hesel

## ✅ Řešení

### Automatické <PERSON>í (Doporučeno):
1. **Otevřete `fix_admin_password.php`**
2. **Klikněte na "🔧 Opravit heslo admin uživatele"**
3. **Otestujte přihlášení** pomocí tlačítka "Test přihlášení"

### Manuální řešení:
```sql
UPDATE inventory_users SET password = 'admin123' WHERE username = 'admin';
```

### Reset kompletní (pokud nic nefunguje):
1. **Otevřete `fix_admin_password.php`**
2. **Klikněte na "🗑️ Reset všech inventurních tabulek"**
3. **Tabulky se znovu vytvoří s správným admin uživatelem**

## 🧪 Diagnostické nástroje

### 1. **`debug_prihlaseni.php`** - Kompletní diagnostika
- Kontroluje konfiguraci databáze
- Testuje připojení
- Ověřuje strukturu tabulek
- Testuje admin uživatele a heslo
- Testuje API volání

### 2. **`fix_admin_password.php`** - Oprava hesla
- Zobrazuje současný stav admin uživatele
- Opravuje heslo na správnou hodnotu
- Testuje přihlášení po opravě

### 3. **`test_oprava_prihlaseni.php`** - Test opravy
- Ověřuje, že oprava přihlášení funguje
- Testuje API s novými parametry

## 📋 Krok za krokem řešení

### Krok 1: Diagnostika
```bash
# Otevřete v prohlížeči:
debug_prihlaseni.php
```

### Krok 2: Oprava hesla
```bash
# Otevřete v prohlížeči:
fix_admin_password.php
# Klikněte na "Opravit heslo admin uživatele"
```

### Krok 3: Test přihlášení
```bash
# Otevřete aplikaci:
index.html
# Přihlaste se: admin / admin123
```

## 🔧 Technické detaily

### Původní problém:
```php
// V utils/database.php (ŠPATNĚ):
$password = 'admin123';  // Čistý text
$stmt->execute(['password' => $password]);
```

### Oprava:
```php
// V utils/database.php (SPRÁVNĚ):
$password = password_hash('admin123', PASSWORD_DEFAULT);  // Hashované
$stmt->execute(['password' => $password]);
```

### Ale pak se ukázalo, že jednodušší je:
```php
// Finální řešení (NEJJEDNODUŠŠÍ):
$password = 'admin123';  // Čistý text pro jednoduchost
$stmt->execute(['password' => $password]);
```

## ⚠️ Důležité poznámky

### Bezpečnost:
- V produkčním prostředí by hesla měla být hashovaná
- Pro testování používáme čistý text pro jednoduchost
- Admin heslo je `admin123` (změňte v produkci!)

### Kompatibilita:
- Funkce `authenticateUser()` podporuje různé typy hesel
- Automaticky detekuje hashovaná vs. čistý text
- Má fallback mechanismy pro různé scénáře

## 🎉 Očekávaný výsledek

Po úspěšné opravě:

### ✅ **Debug přihlášení ukáže:**
```
✓ ÚSPĚŠNÉ - Konfigurace načtena
✓ ÚSPĚŠNÉ - Připojení k databázi funguje
✓ ÚSPĚŠNÉ - Tabulka inventory_users existuje
✓ ÚSPĚŠNÉ - Admin uživatel existuje
✓ ÚSPĚŠNÉ - Heslo admin123 je správné
✓ ÚSPĚŠNÉ - API check funguje
✓ ÚSPĚŠNÉ - API login funguje správně
```

### ✅ **Aplikace se přihlásí:**
- Uživatelské jméno: `admin`
- Heslo: `admin123`
- Úspěšné přesměrování na dashboard

## 🆘 Pokud problém přetrvává

### 1. Zkontrolujte error log:
```bash
# Windows (XAMPP):
C:\xampp\apache\logs\error.log

# Linux:
/var/log/apache2/error.log
```

### 2. Zkontrolujte konzoli prohlížeče:
- Otevřete F12
- Záložka "Console"
- Hledejte chyby JavaScript

### 3. Zkontrolujte síťové požadavky:
- F12 → záložka "Network"
- Pokuste se přihlásit
- Zkontrolujte odpovědi API

### 4. Reset kompletní:
```bash
# Smazání všech inventurních tabulek a znovu vytvoření:
fix_admin_password.php → "Reset všech inventurních tabulek"
```

## 📞 Další pomoc

Pokud žádné z výše uvedených řešení nepomůže:
1. Spusťte všechny diagnostické nástroje
2. Zkopírujte výsledky error logů
3. Zkontrolujte, že webový server běží správně
4. Ověřte oprávnění souborů

---
*Návod pro inventurní systém v1.5*
