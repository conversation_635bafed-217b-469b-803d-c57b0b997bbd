<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test načítání aplikace</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f8f9fa;
        }
        .test-result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            background: white;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .button {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        .button:hover { background: #0056b3; }
        .button.success { background: #28a745; }
        .button.success:hover { background: #218838; }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Test načítání aplikace</h1>
    
    <div class="test-result">
        <h3>📋 Co testujeme:</h3>
        <ul>
            <li><strong>Rychlost načtení</strong> - aplikace se načte do 3 sekund</li>
            <li><strong>Zobrazení přihlášení</strong> - formulář se zobrazí okamžitě</li>
            <li><strong>Databázové informace</strong> - zobrazí se statické informace</li>
            <li><strong>Žádné zasekávání</strong> - žádné "Načítání..." zprávy</li>
        </ul>
    </div>
    
    <div class="test-result">
        <h3>🎯 Očekávaný výsledek:</h3>
        <p><strong>✅ Aplikace se načte okamžitě</strong> s přihlašovacím formulářem</p>
        <p><strong>✅ Zobrazí se informace:</strong> "✓ Připojení k databázi je aktivní"</p>
        <p><strong>✅ Žádné čekání</strong> na načítání databázových informací</p>
    </div>
    
    <div style="text-align: center; margin: 20px 0;">
        <button onclick="testApp()" class="button">🚀 Spustit test aplikace</button>
        <button onclick="reloadTest()" class="button">🔄 Znovu načíst test</button>
        <a href="index.html" class="button success" target="_blank">📱 Otevřít aplikaci v novém okně</a>
    </div>
    
    <div id="test-status" class="test-result" style="display: none;">
        <h3>📊 Výsledek testu:</h3>
        <div id="test-results"></div>
    </div>
    
    <div id="app-container" style="display: none;">
        <h3>📱 Aplikace v iframe:</h3>
        <iframe id="app-iframe" src="about:blank"></iframe>
    </div>
    
    <div class="test-result">
        <h3>🔧 Pokud test selže:</h3>
        <p><a href="okamzita_oprava.php" class="button">🚨 Okamžitá oprava</a></p>
        <p><a href="rychla_oprava_db.php" class="button">🔧 Rychlá oprava DB</a></p>
        <p><a href="spustit_test.php" class="button">📋 Testovací dashboard</a></p>
    </div>

    <script>
        let testStartTime;
        
        function testApp() {
            console.log('🧪 Spouštím test aplikace...');
            
            const statusDiv = document.getElementById('test-status');
            const resultsDiv = document.getElementById('test-results');
            const containerDiv = document.getElementById('app-container');
            const iframe = document.getElementById('app-iframe');
            
            // Zobrazíme test status
            statusDiv.style.display = 'block';
            containerDiv.style.display = 'block';
            
            // Resetujeme výsledky
            resultsDiv.innerHTML = '<p style="color: blue;">🔄 Testování probíhá...</p>';
            
            // Zaznamenáme čas začátku
            testStartTime = Date.now();
            
            // Načteme aplikaci do iframe
            iframe.src = 'index.html';
            
            // Nastavíme timeout pro test
            setTimeout(() => {
                checkAppLoaded();
            }, 5000); // 5 sekund na načtení
            
            // Posloucháme načtení iframe
            iframe.onload = function() {
                setTimeout(() => {
                    checkAppLoaded();
                }, 1000); // Počkáme 1 sekundu na JavaScript
            };
        }
        
        function checkAppLoaded() {
            const resultsDiv = document.getElementById('test-results');
            const iframe = document.getElementById('app-iframe');
            const loadTime = Date.now() - testStartTime;
            
            let results = [];
            let allPassed = true;
            
            try {
                // Test 1: Rychlost načtení
                if (loadTime < 3000) {
                    results.push('✅ <strong>Rychlost načtení:</strong> ' + loadTime + 'ms (VÝBORNÉ)');
                } else if (loadTime < 5000) {
                    results.push('⚠️ <strong>Rychlost načtení:</strong> ' + loadTime + 'ms (POMALÉ)');
                    allPassed = false;
                } else {
                    results.push('❌ <strong>Rychlost načtení:</strong> ' + loadTime + 'ms (PŘÍLIŠ POMALÉ)');
                    allPassed = false;
                }
                
                // Test 2: Kontrola obsahu iframe
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    
                    // Test přihlašovacího formuláře
                    const loginForm = iframeDoc.querySelector('form');
                    if (loginForm) {
                        results.push('✅ <strong>Přihlašovací formulář:</strong> Nalezen');
                    } else {
                        results.push('❌ <strong>Přihlašovací formulář:</strong> Nenalezen');
                        allPassed = false;
                    }
                    
                    // Test databázových informací
                    const dbInfo = iframeDoc.querySelector('#db-info');
                    if (dbInfo) {
                        const dbText = dbInfo.textContent || dbInfo.innerText;
                        if (dbText.includes('Připojení k databázi je aktivní')) {
                            results.push('✅ <strong>Databázové informace:</strong> Zobrazeny správně');
                        } else if (dbText.includes('Načítání')) {
                            results.push('❌ <strong>Databázové informace:</strong> Pořád se načítá');
                            allPassed = false;
                        } else {
                            results.push('⚠️ <strong>Databázové informace:</strong> Neočekávaný obsah');
                            allPassed = false;
                        }
                    } else {
                        results.push('❌ <strong>Databázové informace:</strong> Element nenalezen');
                        allPassed = false;
                    }
                    
                    // Test uživatelského jména
                    const usernameInput = iframeDoc.querySelector('input[type="text"], input[placeholder*="jméno"]');
                    if (usernameInput) {
                        results.push('✅ <strong>Pole uživatelské jméno:</strong> Nalezeno');
                    } else {
                        results.push('❌ <strong>Pole uživatelské jméno:</strong> Nenalezeno');
                        allPassed = false;
                    }
                    
                    // Test hesla
                    const passwordInput = iframeDoc.querySelector('input[type="password"]');
                    if (passwordInput) {
                        results.push('✅ <strong>Pole heslo:</strong> Nalezeno');
                    } else {
                        results.push('❌ <strong>Pole heslo:</strong> Nenalezeno');
                        allPassed = false;
                    }
                    
                } catch (e) {
                    results.push('❌ <strong>Přístup k obsahu:</strong> Blokován (CORS)');
                    results.push('ℹ️ <strong>Info:</strong> Otevřete aplikaci v novém okně pro plný test');
                    allPassed = false;
                }
                
            } catch (error) {
                results.push('❌ <strong>Chyba testu:</strong> ' + error.message);
                allPassed = false;
            }
            
            // Zobrazíme výsledky
            let resultHtml = results.join('<br>') + '<br><br>';
            
            if (allPassed) {
                resultHtml += '<div style="color: green; font-weight: bold; font-size: 18px;">🎉 VŠECHNY TESTY PROŠLY!</div>';
                resultHtml += '<p>Aplikace je připravena k použití. Můžete se přihlásit s údaji: <strong>admin / admin123</strong></p>';
            } else {
                resultHtml += '<div style="color: red; font-weight: bold; font-size: 18px;">❌ NĚKTERÉ TESTY SELHALY</div>';
                resultHtml += '<p>Použijte nástroje pro opravu níže.</p>';
            }
            
            resultsDiv.innerHTML = resultHtml;
        }
        
        function reloadTest() {
            const iframe = document.getElementById('app-iframe');
            iframe.src = 'about:blank';
            setTimeout(() => {
                testApp();
            }, 500);
        }
        
        // Automaticky spustíme test při načtení stránky
        window.onload = function() {
            setTimeout(() => {
                testApp();
            }, 1000);
        };
    </script>
</body>
</html>
