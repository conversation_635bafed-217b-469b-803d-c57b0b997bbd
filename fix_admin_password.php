<?php
/**
 * Oprava hesla admin uživatele
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='cs'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Oprava hesla admin uživatele</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".ok { color: green; font-weight: bold; }";
echo ".error { color: red; font-weight: bold; }";
echo ".warning { color: orange; font-weight: bold; }";
echo ".info { color: blue; font-weight: bold; }";
echo ".test { margin: 15px 0; padding: 15px; border-left: 4px solid #ccc; background: #f9f9f9; }";
echo "pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }";
echo ".button { display: inline-block; padding: 10px 20px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; border: none; cursor: pointer; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 Oprava hesla admin uživatele</h1>";

if (isset($_POST['action'])) {
    if ($_POST['action'] === 'fix_password') {
        echo "<div class='test'>";
        echo "<h3>Oprava hesla admin uživatele</h3>";
        
        try {
            // Načtení konfigurace a připojení k databázi
            $config = require_once 'config/database.php';
            $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
            $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
            
            echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Připojení k databázi<br>";
            
            // Kontrola existence tabulky
            $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_users'");
            if ($stmt->rowCount() == 0) {
                echo "<span class='error'>✗ CHYBA</span> - Tabulka inventory_users neexistuje<br>";
                echo "<p>Spusťte nejprve <a href='check_tables.php'>check_tables.php</a></p>";
            } else {
                echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Tabulka inventory_users existuje<br>";
                
                // Kontrola admin uživatele
                $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE username = 'admin'");
                $stmt->execute();
                $admin = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($admin) {
                    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Admin uživatel existuje<br>";
                    echo "<strong>Současné údaje:</strong><br>";
                    echo "- ID: " . $admin['id'] . "<br>";
                    echo "- Username: " . $admin['username'] . "<br>";
                    echo "- Role: " . $admin['role'] . "<br>";
                    echo "- Active: " . ($admin['active'] ? 'Ano' : 'Ne') . "<br>";
                    echo "- Současné heslo: " . substr($admin['password'], 0, 30) . "...<br>";
                    
                    // Test současného hesla
                    $currentPasswordWorks = false;
                    if (password_verify('admin123', $admin['password'])) {
                        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Současné heslo funguje s password_verify<br>";
                        $currentPasswordWorks = true;
                    } elseif ($admin['password'] === 'admin123') {
                        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Současné heslo je v čistém textu<br>";
                        $currentPasswordWorks = true;
                    } else {
                        echo "<span class='error'>✗ PROBLÉM</span> - Současné heslo nefunguje<br>";
                    }
                    
                    if (!$currentPasswordWorks) {
                        // Oprava hesla
                        echo "<span class='info'>ℹ INFO</span> - Opravujem heslo...<br>";
                        
                        // Nastavíme heslo na čistý text pro jednoduchost
                        $stmt = $pdo->prepare("UPDATE inventory_users SET password = 'admin123' WHERE username = 'admin'");
                        $result = $stmt->execute();
                        
                        if ($result) {
                            echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Heslo opraveno na 'admin123' (čistý text)<br>";
                            
                            // Ověření opravy
                            $stmt = $pdo->prepare("SELECT password FROM inventory_users WHERE username = 'admin'");
                            $stmt->execute();
                            $newPassword = $stmt->fetchColumn();
                            
                            if ($newPassword === 'admin123') {
                                echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Oprava ověřena<br>";
                            } else {
                                echo "<span class='error'>✗ CHYBA</span> - Oprava se nezdařila<br>";
                            }
                        } else {
                            echo "<span class='error'>✗ CHYBA</span> - Nepodařilo se opravit heslo<br>";
                        }
                    }
                    
                } else {
                    echo "<span class='error'>✗ CHYBA</span> - Admin uživatel neexistuje<br>";
                    
                    // Vytvoření admin uživatele
                    echo "<span class='info'>ℹ INFO</span> - Vytvářím admin uživatele...<br>";
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO inventory_users (username, password, role, full_name, email, active) 
                        VALUES ('admin', 'admin123', 'admin', 'Administrátor', '<EMAIL>', 1)
                    ");
                    
                    $result = $stmt->execute();
                    
                    if ($result) {
                        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Admin uživatel vytvořen<br>";
                    } else {
                        echo "<span class='error'>✗ CHYBA</span> - Nepodařilo se vytvořit admin uživatele<br>";
                        echo "<pre>" . print_r($stmt->errorInfo(), true) . "</pre>";
                    }
                }
            }
            
        } catch (Exception $e) {
            echo "<span class='error'>✗ CHYBA</span> - " . $e->getMessage() . "<br>";
        }
        
        echo "</div>";
        
        // Test přihlášení
        echo "<div class='test'>";
        echo "<h3>Test přihlášení po opravě</h3>";
        echo "<button onclick='testLogin()' class='button'>Test přihlášení</button>";
        echo "<div id='login-result' style='margin-top: 10px;'></div>";
        echo "</div>";
        
    } elseif ($_POST['action'] === 'reset_tables') {
        echo "<div class='test'>";
        echo "<h3>Reset inventurních tabulek</h3>";
        
        try {
            require_once 'utils/database.php';
            
            // Smazání a znovu vytvoření tabulek
            $pdo = getDbConnection();
            
            echo "<span class='info'>ℹ INFO</span> - Mazání existujících tabulek...<br>";
            
            $tables = ['inventory_entries', 'inventory_sessions', 'inventory_stock_changes', 'previous_stock', 'inventory_totals', 'inventory_users'];
            
            foreach ($tables as $table) {
                try {
                    $pdo->exec("DROP TABLE IF EXISTS `$table`");
                    echo "<span class='ok'>✓</span> Tabulka $table smazána<br>";
                } catch (Exception $e) {
                    echo "<span class='warning'>⚠</span> Tabulka $table: " . $e->getMessage() . "<br>";
                }
            }
            
            echo "<span class='info'>ℹ INFO</span> - Vytváření nových tabulek...<br>";
            
            // Znovu vytvoření tabulek
            ensureTablesExist();
            
            echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Tabulky znovu vytvořeny<br>";
            
        } catch (Exception $e) {
            echo "<span class='error'>✗ CHYBA</span> - " . $e->getMessage() . "<br>";
        }
        
        echo "</div>";
    }
} else {
    // Zobrazení současného stavu
    echo "<div class='test'>";
    echo "<h3>Současný stav admin uživatele</h3>";
    
    try {
        $config = require_once 'config/database.php';
        $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
        $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
        
        $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_users'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE username = 'admin'");
            $stmt->execute();
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($admin) {
                echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Admin uživatel existuje<br>";
                echo "<strong>Údaje:</strong><br>";
                echo "- Username: " . $admin['username'] . "<br>";
                echo "- Role: " . $admin['role'] . "<br>";
                echo "- Active: " . ($admin['active'] ? 'Ano' : 'Ne') . "<br>";
                echo "- Heslo: " . substr($admin['password'], 0, 20) . "...<br>";
                
                // Test hesla
                if (password_verify('admin123', $admin['password'])) {
                    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Heslo funguje (hashované)<br>";
                } elseif ($admin['password'] === 'admin123') {
                    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Heslo funguje (čistý text)<br>";
                } else {
                    echo "<span class='error'>✗ PROBLÉM</span> - Heslo nefunguje<br>";
                }
            } else {
                echo "<span class='error'>✗ PROBLÉM</span> - Admin uživatel neexistuje<br>";
            }
        } else {
            echo "<span class='error'>✗ PROBLÉM</span> - Tabulka inventory_users neexistuje<br>";
        }
        
    } catch (Exception $e) {
        echo "<span class='error'>✗ CHYBA</span> - " . $e->getMessage() . "<br>";
    }
    
    echo "</div>";
    
    // Formulář pro akce
    echo "<div class='test'>";
    echo "<h3>Dostupné akce</h3>";
    
    echo "<form method='post' style='margin: 10px 0;'>";
    echo "<button type='submit' name='action' value='fix_password' class='button'>🔧 Opravit heslo admin uživatele</button>";
    echo "</form>";
    
    echo "<form method='post' style='margin: 10px 0;'>";
    echo "<button type='submit' name='action' value='reset_tables' class='button' style='background: #dc3545;'>🗑️ Reset všech inventurních tabulek</button>";
    echo "<p><small style='color: #dc3545;'>⚠️ Toto smaže všechna data v inventurních tabulkách!</small></p>";
    echo "</form>";
    
    echo "</div>";
}

echo "<script>";
echo "async function testLogin() {";
echo "    const resultDiv = document.getElementById('login-result');";
echo "    resultDiv.innerHTML = '<span style=\"color: blue;\">Testování přihlášení...</span>';";
echo "    ";
echo "    try {";
echo "        const response = await fetch('api/simple_auth.php', {";
echo "            method: 'POST',";
echo "            headers: { 'Content-Type': 'application/json' },";
echo "            body: JSON.stringify({ action: 'login', username: 'admin', password: 'admin123' })";
echo "        });";
echo "        ";
echo "        const data = await response.json();";
echo "        ";
echo "        if (data.success) {";
echo "            resultDiv.innerHTML = '<span style=\"color: green; font-weight: bold;\">✓ ÚSPĚŠNÉ - Přihlášení funguje!</span>';";
echo "        } else {";
echo "            resultDiv.innerHTML = '<span style=\"color: red; font-weight: bold;\">✗ NEÚSPĚŠNÉ - ' + (data.error || 'Neznámá chyba') + '</span>';";
echo "        }";
echo "    } catch (error) {";
echo "        resultDiv.innerHTML = '<span style=\"color: red; font-weight: bold;\">✗ CHYBA - ' + error.message + '</span>';";
echo "    }";
echo "}";
echo "</script>";

echo "<p><a href='debug_prihlaseni.php' class='button'>Debug přihlášení</a> <a href='index.html' class='button'>Zkusit aplikaci</a></p>";

echo "</body>";
echo "</html>";
?>
