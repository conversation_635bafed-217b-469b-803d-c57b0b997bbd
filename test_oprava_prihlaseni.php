<?php
/**
 * Test opravy přihlašovacího systému
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='cs'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Test opravy přihlášení</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".ok { color: green; font-weight: bold; }";
echo ".error { color: red; font-weight: bold; }";
echo ".warning { color: orange; font-weight: bold; }";
echo ".test { margin: 10px 0; padding: 10px; border-left: 3px solid #ccc; }";
echo "pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 Test opravy přih<PERSON>ovacího systému</h1>";

// Test 1: Kontrola API check
echo "<div class='test'>";
echo "<h3>Test 1: API check (GET s parametrem v URL)</h3>";
$url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/simple_auth.php?action=check";
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$response = @file_get_contents($url, false, $context);
if ($response !== false) {
    $data = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - API check funguje<br>";
        echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<span class='error'>✗ CHYBA</span> - API nevrací platný JSON<br>";
        echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
    }
} else {
    echo "<span class='error'>✗ CHYBA</span> - API neodpovídá<br>";
}
echo "</div>";

// Test 2: Kontrola API login s akcí v těle
echo "<div class='test'>";
echo "<h3>Test 2: API login (POST s akcí v těle)</h3>";

// Nejprve zkontrolujeme, zda existuje admin uživatel
try {
    $config = require_once 'config/database.php';
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    
    // Zajistíme, že tabulky existují
    require_once 'utils/database.php';
    ensureTablesExist();
    
    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Tabulky zkontrolovány<br>";
} catch (Exception $e) {
    echo "<span class='error'>✗ CHYBA</span> - Problém s databází: " . $e->getMessage() . "<br>";
}

$loginData = json_encode([
    'action' => 'login',
    'username' => 'admin',
    'password' => 'admin123'
]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $loginData,
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$loginResponse = @file_get_contents("http://localhost/PU/INVENTURA%20X/INVX1.5/api/simple_auth.php", false, $context);
if ($loginResponse !== false) {
    $loginJson = json_decode($loginResponse, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        if (isset($loginJson['success']) && $loginJson['success']) {
            echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - API login funguje správně<br>";
        } else {
            echo "<span class='warning'>⚠ VAROVÁNÍ</span> - API login odpovídá, ale přihlášení neúspěšné<br>";
        }
        echo "<pre>" . json_encode($loginJson, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<span class='error'>✗ CHYBA</span> - API login nevrací platný JSON<br>";
        echo "<pre>" . htmlspecialchars(substr($loginResponse, 0, 500)) . "</pre>";
    }
} else {
    echo "<span class='error'>✗ CHYBA</span> - API login neodpovídá<br>";
}
echo "</div>";

// Test 3: Kontrola JavaScript konzole
echo "<div class='test'>";
echo "<h3>Test 3: Kontrola JavaScript konzole</h3>";
echo "<p>Otevřete <a href='index.html' target='_blank'>aplikaci</a> a zkontrolujte konzoli prohlížeče (F12).</p>";
echo "<p>Měli byste vidět:</p>";
echo "<ul>";
echo "<li><code>buildApiUrl - endpoint: simple_auth</code></li>";
echo "<li><code>buildApiUrl - params: {}</code></li>";
echo "<li><code>handleLogin - URL pro přihlášení: api/simple_auth.php</code></li>";
echo "<li><code>handleLogin - data pro přihlášení: {\"action\":\"login\",\"username\":\"admin\",\"password\":\"admin123\"}</code></li>";
echo "</ul>";
echo "</div>";

// Test 4: Simulace přihlášení přes JavaScript
echo "<div class='test'>";
echo "<h3>Test 4: Simulace přihlášení přes JavaScript</h3>";
echo "<button onclick='testLogin()' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;'>Testovat přihlášení</button>";
echo "<div id='login-result' style='margin-top: 10px;'></div>";

echo "<script>";
echo "async function testLogin() {";
echo "    const resultDiv = document.getElementById('login-result');";
echo "    resultDiv.innerHTML = '<p style=\"color: blue;\">Testování přihlášení...</p>';";
echo "    ";
echo "    try {";
echo "        const response = await fetch('api/simple_auth.php', {";
echo "            method: 'POST',";
echo "            headers: {";
echo "                'Content-Type': 'application/json'";
echo "            },";
echo "            body: JSON.stringify({";
echo "                action: 'login',";
echo "                username: 'admin',";
echo "                password: 'admin123'";
echo "            })";
echo "        });";
echo "        ";
echo "        const data = await response.json();";
echo "        ";
echo "        if (data.success) {";
echo "            resultDiv.innerHTML = '<p style=\"color: green; font-weight: bold;\">✓ ÚSPĚŠNÉ - Přihlášení funguje!</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';";
echo "        } else {";
echo "            resultDiv.innerHTML = '<p style=\"color: orange; font-weight: bold;\">⚠ VAROVÁNÍ - Přihlášení neúspěšné</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';";
echo "        }";
echo "    } catch (error) {";
echo "        resultDiv.innerHTML = '<p style=\"color: red; font-weight: bold;\">✗ CHYBA - ' + error.message + '</p>';";
echo "    }";
echo "}";
echo "</script>";
echo "</div>";

echo "<h2>📋 Souhrn oprav</h2>";
echo "<ul>";
echo "<li><strong>JavaScript:</strong> Akce se nyní posílá v těle POST požadavku místo v URL</li>";
echo "<li><strong>API:</strong> Zpracovává akce jak z URL (GET) tak z těla (POST)</li>";
echo "<li><strong>Kompatibilita:</strong> Zachována zpětná kompatibilita pro GET požadavky</li>";
echo "</ul>";

echo "<p><a href='spustit_test.php'>← Zpět na testovací dashboard</a></p>";

echo "</body>";
echo "</html>";
?>
