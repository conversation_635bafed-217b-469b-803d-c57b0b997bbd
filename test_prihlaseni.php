<?php
/**
 * Test přihlašovacího systému
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='cs'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Test přihlášení</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".ok { color: green; font-weight: bold; }";
echo ".error { color: red; font-weight: bold; }";
echo ".warning { color: orange; font-weight: bold; }";
echo ".test { margin: 10px 0; padding: 10px; border-left: 3px solid #ccc; }";
echo "pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔐 Test přihlašovacího systému</h1>";

// Test 1: Kontrola API souboru
echo "<div class='test'>";
echo "<h3>Test 1: Kontrola API autentifikace</h3>";
$authFile = 'api/simple_auth.php';
if (file_exists($authFile)) {
    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Soubor $authFile existuje<br>";
    
    // Zkontrolujeme obsah souboru
    $content = file_get_contents($authFile);
    if (strpos($content, 'action') !== false) {
        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Soubor obsahuje zpracování akcí<br>";
    } else {
        echo "<span class='warning'>⚠ VAROVÁNÍ</span> - Soubor možná neobsahuje správné zpracování akcí<br>";
    }
} else {
    echo "<span class='error'>✗ CHYBA</span> - Soubor $authFile neexistuje<br>";
}
echo "</div>";

// Test 2: Kontrola databáze a tabulek
echo "<div class='test'>";
echo "<h3>Test 2: Kontrola databáze a uživatelských tabulek</h3>";
try {
    $config = require_once 'config/database.php';
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    
    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Připojení k databázi funguje<br>";
    
    // Kontrola tabulky inventory_users
    $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_users'");
    if ($stmt->rowCount() > 0) {
        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Tabulka inventory_users existuje<br>";
        
        // Kontrola admin uživatele
        $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE username = 'admin'");
        $stmt->execute();
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin) {
            echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Admin uživatel existuje<br>";
            echo "<strong>Admin údaje:</strong><br>";
            echo "- ID: " . $admin['id'] . "<br>";
            echo "- Username: " . $admin['username'] . "<br>";
            echo "- Role: " . $admin['role'] . "<br>";
            echo "- Active: " . ($admin['active'] ? 'Ano' : 'Ne') . "<br>";
            echo "- Password hash: " . substr($admin['password'], 0, 20) . "...<br>";
        } else {
            echo "<span class='error'>✗ CHYBA</span> - Admin uživatel neexistuje<br>";
        }
        
        // Zobrazíme všechny uživatele
        $stmt = $pdo->query("SELECT username, role, active FROM inventory_users");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<strong>Všichni uživatelé:</strong><br>";
        foreach ($users as $user) {
            echo "- {$user['username']} ({$user['role']}) - " . ($user['active'] ? 'Aktivní' : 'Neaktivní') . "<br>";
        }
    } else {
        echo "<span class='error'>✗ CHYBA</span> - Tabulka inventory_users neexistuje<br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>✗ CHYBA</span> - " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 3: Test API volání
echo "<div class='test'>";
echo "<h3>Test 3: Test API volání</h3>";

// Test check akce
echo "<h4>Test check akce:</h4>";
$url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/simple_auth.php?action=check";
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$response = @file_get_contents($url, false, $context);
if ($response !== false) {
    $data = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - API check funguje<br>";
        echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<span class='error'>✗ CHYBA</span> - API nevrací platný JSON<br>";
        echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
    }
} else {
    echo "<span class='error'>✗ CHYBA</span> - API neodpovídá<br>";
}

// Test login akce
echo "<h4>Test login akce:</h4>";
$loginData = json_encode([
    'action' => 'login',
    'username' => 'admin',
    'password' => 'admin123'
]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $loginData,
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$loginResponse = @file_get_contents($url, false, $context);
if ($loginResponse !== false) {
    $loginJson = json_decode($loginResponse, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - API login odpovídá<br>";
        echo "<pre>" . json_encode($loginJson, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<span class='error'>✗ CHYBA</span> - API login nevrací platný JSON<br>";
        echo "<pre>" . htmlspecialchars(substr($loginResponse, 0, 500)) . "</pre>";
    }
} else {
    echo "<span class='error'>✗ CHYBA</span> - API login neodpovídá<br>";
}

echo "</div>";

// Test 4: Kontrola session
echo "<div class='test'>";
echo "<h3>Test 4: Kontrola session</h3>";
session_start();
echo "<strong>Session ID:</strong> " . session_id() . "<br>";
echo "<strong>Session save path:</strong> " . session_save_path() . "<br>";
echo "<strong>Session obsah:</strong><br>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";
echo "</div>";

echo "<h2>🔧 Doporučené akce:</h2>";
echo "<ol>";
echo "<li>Pokud tabulka inventory_users neexistuje, spusťte <a href='check_tables.php' target='_blank'>check_tables.php</a></li>";
echo "<li>Pokud admin uživatel neexistuje, bude vytvořen automaticky</li>";
echo "<li>Pokud API nefunguje, zkontrolujte error log webového serveru</li>";
echo "<li>Zkuste se přihlásit přímo přes <a href='index.html' target='_blank'>aplikaci</a></li>";
echo "</ol>";

echo "<p><a href='spustit_test.php'>← Zpět na testovací dashboard</a></p>";

echo "</body>";
echo "</html>";
?>
