<?php
/**
 * Database Connection Utility
 *
 * This file provides a function to connect to the database using PDO.
 */

/**
 * Get a PDO database connection
 *
 * @return PDO The database connection
 * @throws PDOException If connection fails
 */
function getDbConnection() {
    static $pdo;

    if (!$pdo) {
        error_log("getDbConnection - začátek připojování k databázi");
        $config = require __DIR__ . '/../config/database.php';
        error_log("getDbConnection - konfigurace načtena: host=" . $config['host'] . ", dbname=" . $config['dbname']);

        $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
        error_log("getDbConnection - DSN: " . $dsn);

        try {
            error_log("getDbConnection - pokus o připojení k databázi");
            $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
            error_log("getDbConnection - připojení k databázi úspěšné");

            // Automatická inicializace tabulek při prvním připojení
            static $initialized = false;
            if (!$initialized) {
                error_log("getDbConnection - inicializace tabulek");
                ensureTablesExist();
                $initialized = true;
                error_log("getDbConnection - inicializace tabulek dokončena");
            }
        } catch (PDOException $e) {
            // Log the error but don't expose sensitive information
            error_log("getDbConnection - chyba při připojení k databázi: " . $e->getMessage());
            throw new PDOException("Database connection failed. Please check the configuration.");
        }
    }

    return $pdo;
}

/**
 * Check if the required custom tables exist, create them if they don't
 *
 * @return bool True if tables exist or were created successfully
 * @throws PDOException If table creation fails
 */
function ensureTablesExist() {
    error_log("ensureTablesExist - začátek kontroly tabulek");
    $pdo = getDbConnection();
    error_log("ensureTablesExist - připojení k databázi úspěšné");

    // Check if our custom tables exist
    $tables = [
        'inventory_sessions',
        'inventory_entries',
        'inventory_stock_changes',
        'previous_stock',
        'inventory_users'
    ];
    error_log("ensureTablesExist - kontrola tabulek: " . implode(", ", $tables));

    $missingTables = [];

    foreach ($tables as $table) {
        error_log("ensureTablesExist - kontrola tabulky: " . $table);
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = :dbname
            AND TABLE_NAME = :table
        ");

        $dbname = $pdo->query("SELECT DATABASE()")->fetchColumn();
        error_log("ensureTablesExist - aktuální databáze: " . $dbname);

        $stmt->execute([
            'dbname' => $dbname,
            'table' => $table
        ]);

        $count = $stmt->fetchColumn();
        error_log("ensureTablesExist - tabulka " . $table . " existuje: " . ($count > 0 ? "ANO" : "NE"));

        if ($count === 0) {
            $missingTables[] = $table;
        }
    }

    error_log("ensureTablesExist - chybějící tabulky: " . implode(", ", $missingTables));

    // If any tables are missing, create them
    if (!empty($missingTables)) {
        try {
            // Create inventory_users table first (needed for foreign keys)
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS `inventory_users` (
                  `id` INT AUTO_INCREMENT PRIMARY KEY,
                  `username` VARCHAR(255) NOT NULL UNIQUE,
                  `password` VARCHAR(255) NOT NULL,
                  `role` ENUM('admin', 'manager', 'user') NOT NULL DEFAULT 'user',
                  `full_name` VARCHAR(255) NOT NULL,
                  `email` VARCHAR(255) NULL,
                  `active` BOOLEAN NOT NULL DEFAULT TRUE,
                  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  INDEX `idx_inventory_users_username` (`username`),
                  INDEX `idx_inventory_users_role` (`role`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");

            // Create inventory_sessions table
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS `inventory_sessions` (
                  `id` INT AUTO_INCREMENT PRIMARY KEY,
                  `start_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `end_time` DATETIME NULL,
                  `status` ENUM('active', 'completed', 'cancelled') NOT NULL DEFAULT 'active',
                  `user_id` INT NOT NULL,
                  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  FOREIGN KEY (`user_id`) REFERENCES `inventory_users` (`id`) ON DELETE CASCADE,
                  INDEX `idx_inventory_sessions_status` (`status`),
                  INDEX `idx_inventory_sessions_user` (`user_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");

            // Create inventory_entries table
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS `inventory_entries` (
                  `id` INT AUTO_INCREMENT PRIMARY KEY,
                  `session_id` INT NOT NULL,
                  `product_id` VARCHAR(255) NOT NULL,
                  `ean_code` VARCHAR(255) NOT NULL,
                  `user_id` INT NOT NULL,
                  `zadane_mnozstvi` DECIMAL(10,3) NOT NULL DEFAULT 0,
                  `last_updated` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  `status` ENUM('active', 'deleted') NOT NULL DEFAULT 'active',
                  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  FOREIGN KEY (`session_id`) REFERENCES `inventory_sessions` (`id`) ON DELETE CASCADE,
                  FOREIGN KEY (`user_id`) REFERENCES `inventory_users` (`id`) ON DELETE CASCADE,
                  INDEX `idx_inventory_entries_product` (`product_id`),
                  INDEX `idx_inventory_entries_session` (`session_id`),
                  INDEX `idx_inventory_entries_user` (`user_id`),
                  INDEX `idx_inventory_entries_status` (`status`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");

            // Create inventory_stock_changes table
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS `inventory_stock_changes` (
                  `id` INT AUTO_INCREMENT PRIMARY KEY,
                  `session_id` INT NOT NULL,
                  `product_id` VARCHAR(255) NOT NULL,
                  `initial_stock` DECIMAL(10,3) NOT NULL DEFAULT 0,
                  `stock_changes_during_inventory` DECIMAL(10,3) NOT NULL DEFAULT 0,
                  `last_update` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  FOREIGN KEY (`session_id`) REFERENCES `inventory_sessions` (`id`) ON DELETE CASCADE,
                  UNIQUE KEY `uk_inventory_stock_changes_session_product` (`session_id`, `product_id`),
                  INDEX `idx_inventory_stock_changes_product` (`product_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");

            // Create previous_stock table
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS `previous_stock` (
                  `product_id` VARCHAR(255) NOT NULL PRIMARY KEY,
                  `units` DECIMAL(10,3) NOT NULL DEFAULT 0,
                  `last_update` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  INDEX `idx_previous_stock_product` (`product_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");



            // Create triggers for stockcurrent changes
            try {
                // Nejprve odstraníme existující triggery, pokud existují
                $pdo->exec("DROP TRIGGER IF EXISTS `update_previous_stock_after_stockcurrent_update`");
                $pdo->exec("DROP TRIGGER IF EXISTS `update_previous_stock_after_stockcurrent_insert`");
                $pdo->exec("DROP TRIGGER IF EXISTS `update_inventory_entries_after_stockcurrent_update`");
                $pdo->exec("DROP TRIGGER IF EXISTS `update_inventory_entries_after_stockcurrent_insert`");

                // Vytvoříme nové triggery pro aktualizaci previous_stock
                $pdo->exec("
                    CREATE TRIGGER `update_previous_stock_after_stockcurrent_update`
                    AFTER UPDATE ON `stockcurrent`
                    FOR EACH ROW
                    BEGIN
                        INSERT INTO `previous_stock` (`product_id`, `units`)
                        VALUES (NEW.product, NEW.units)
                        ON DUPLICATE KEY UPDATE `units` = NEW.units;
                    END
                ");

                $pdo->exec("
                    CREATE TRIGGER `update_previous_stock_after_stockcurrent_insert`
                    AFTER INSERT ON `stockcurrent`
                    FOR EACH ROW
                    BEGIN
                        INSERT INTO `previous_stock` (`product_id`, `units`)
                        VALUES (NEW.product, NEW.units)
                        ON DUPLICATE KEY UPDATE `units` = NEW.units;
                    END
                ");

                // Kontrola, zda existuje tabulka inventory_totals
                $stmt = $pdo->prepare("
                    SELECT COUNT(*)
                    FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_SCHEMA = :dbname
                    AND TABLE_NAME = 'inventory_totals'
                ");

                $dbname = $pdo->query("SELECT DATABASE()")->fetchColumn();
                $stmt->execute(['dbname' => $dbname]);
                $totalsTableExists = $stmt->fetchColumn() > 0;

                if (!$totalsTableExists) {
                    // Vytvoření tabulky inventory_totals
                    $pdo->exec("
                        CREATE TABLE `inventory_totals` (
                          `id` INT AUTO_INCREMENT PRIMARY KEY,
                          `session_id` INT NOT NULL,
                          `product_id` VARCHAR(255) NOT NULL,
                          `total_zadane_mnozstvi` DECIMAL(10,3) NOT NULL DEFAULT 0,
                          `last_updated` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                          `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                          FOREIGN KEY (`session_id`) REFERENCES `inventory_sessions` (`id`) ON DELETE CASCADE,
                          UNIQUE KEY `uk_inventory_totals_session_product` (`session_id`, `product_id`),
                          INDEX `idx_inventory_totals_product` (`product_id`),
                          INDEX `idx_inventory_totals_session` (`session_id`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    ");

                    // Naplnění tabulky inventory_totals existujícími daty
                    $pdo->exec("
                        INSERT INTO inventory_totals (session_id, product_id, total_zadane_mnozstvi)
                        SELECT
                            session_id,
                            product_id,
                            SUM(zadane_mnozstvi) AS total_zadane_mnozstvi
                        FROM
                            inventory_entries
                        WHERE
                            status = 'active'
                        GROUP BY
                            session_id, product_id
                        ON DUPLICATE KEY UPDATE
                            total_zadane_mnozstvi = VALUES(total_zadane_mnozstvi)
                    ");
                }

                // Vytvoříme nové triggery pro aktualizaci celkových součtů
                $pdo->exec("
                    CREATE TRIGGER `update_inventory_entries_after_stockcurrent_update`
                    AFTER UPDATE ON `stockcurrent`
                    FOR EACH ROW
                    BEGIN
                        -- Výpočet rozdílu mezi starou a novou hodnotou
                        DECLARE difference DECIMAL(10,3);
                        SET difference = OLD.units - NEW.units;

                        -- Pokud je rozdíl kladný (snížení stavu), snížíme zadané množství v celkových součtech
                        -- a také v jednotlivých záznamech
                        IF difference > 0 THEN
                            -- Aktualizace celkových součtů
                            UPDATE inventory_totals
                            SET total_zadane_mnozstvi = total_zadane_mnozstvi - difference
                            WHERE product_id = NEW.product
                            AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');

                            -- Aktualizace pouze záznamů administrátora
                            UPDATE inventory_entries
                            SET zadane_mnozstvi = zadane_mnozstvi - difference
                            WHERE product_id = NEW.product
                            AND status = 'active'
                            AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active')
                            AND user_id IN (SELECT id FROM inventory_users WHERE role = 'admin')
                            AND zadane_mnozstvi > 0;
                        END IF;
                    END
                ");

                $pdo->exec("
                    CREATE TRIGGER `update_inventory_entries_after_stockcurrent_insert`
                    AFTER INSERT ON `stockcurrent`
                    FOR EACH ROW
                    BEGIN
                        -- Při vložení nového záznamu neměníme zadané množství
                        -- Toto je zde pouze pro úplnost, ale nemělo by se používat
                    END
                ");
            } catch (PDOException $e) {
                // Ignore trigger creation errors, as they might already exist or require special privileges
                error_log("Trigger creation failed (non-critical): " . $e->getMessage());
            }

            // Ensure default admin user exists
            ensureDefaultAdminExists($pdo);

            return true;
        } catch (PDOException $e) {
            error_log("Table creation failed: " . $e->getMessage());
            throw new PDOException("Failed to create required database tables: " . $e->getMessage());
        }
    } else {
        // Even if tables exist, ensure default admin user exists
        ensureDefaultAdminExists($pdo);
    }

    return true;
}

/**
 * Ensure that the default admin user exists
 *
 * @param PDO $pdo Database connection
 * @return bool True if admin exists or was created successfully
 */
function ensureDefaultAdminExists($pdo) {
    try {
        error_log("ensureDefaultAdminExists - začátek kontroly existence admin uživatele");

        // Check if admin user exists
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM inventory_users WHERE username = 'admin'");
        $stmt->execute();
        $count = $stmt->fetchColumn();
        error_log("ensureDefaultAdminExists - počet admin uživatelů: " . $count);

        if ($count == 0) {
            error_log("ensureDefaultAdminExists - admin uživatel neexistuje, vytvářím nového");

            // Create default admin user (password: admin123)
            $sql = "
                INSERT INTO inventory_users (username, password, role, full_name)
                VALUES ('admin', :password, 'admin', 'Administrator')
            ";
            error_log("ensureDefaultAdminExists - SQL pro vytvoření admin uživatele: " . $sql);

            $stmt = $pdo->prepare($sql);

            // Použijeme hashované heslo pro bezpečnost
            $password = password_hash('admin123', PASSWORD_DEFAULT);
            error_log("ensureDefaultAdminExists - hashované heslo: " . substr($password, 0, 20) . "...");

            $result = $stmt->execute(['password' => $password]);
            error_log("ensureDefaultAdminExists - výsledek execute(): " . ($result ? "TRUE" : "FALSE"));

            if (!$result) {
                error_log("ensureDefaultAdminExists - chyba při vytváření admin uživatele: " . print_r($stmt->errorInfo(), true));
            } else {
                error_log("ensureDefaultAdminExists - admin uživatel vytvořen úspěšně");
            }

            // Kontrola, zda byl admin uživatel skutečně vytvořen
            $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE username = 'admin'");
            $stmt->execute();
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);
            error_log("ensureDefaultAdminExists - vytvořený admin uživatel: " . print_r($admin, true));
        } else {
            error_log("ensureDefaultAdminExists - admin uživatel již existuje");

            // Výpis existujícího admin uživatele
            $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE username = 'admin'");
            $stmt->execute();
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);
            error_log("ensureDefaultAdminExists - existující admin uživatel: " . print_r($admin, true));
        }

        return true;
    } catch (PDOException $e) {
        error_log("ensureDefaultAdminExists - chyba při kontrole/vytváření admin uživatele: " . $e->getMessage());
        return false;
    }
}
