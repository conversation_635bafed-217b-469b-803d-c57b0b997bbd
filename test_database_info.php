<?php
/**
 * Test načítání informací o databázi
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='cs'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Test informací o databázi</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".ok { color: green; font-weight: bold; }";
echo ".error { color: red; font-weight: bold; }";
echo ".warning { color: orange; font-weight: bold; }";
echo ".test { margin: 10px 0; padding: 10px; border-left: 3px solid #ccc; }";
echo "pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }";
echo ".button { display: inline-block; padding: 10px 20px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🗄️ Test informací o databázi</h1>";

// Test 1: Přímé volání API
echo "<div class='test'>";
echo "<h3>Test 1: API database_info.php</h3>";

$url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/database_info.php";
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$response = @file_get_contents($url, false, $context);
if ($response !== false) {
    $data = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        if ($data['success']) {
            echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - API vrací informace o databázi<br>";
            
            echo "<h4>Informace o databázi:</h4>";
            echo "<ul>";
            echo "<li><strong>Server:</strong> " . $data['database_info']['host'] . "</li>";
            echo "<li><strong>Databáze:</strong> " . $data['database_info']['database'] . "</li>";
            echo "<li><strong>Uživatel:</strong> " . $data['database_info']['username'] . "</li>";
            echo "<li><strong>MySQL verze:</strong> " . $data['database_info']['mysql_version'] . "</li>";
            echo "</ul>";
            
            echo "<h4>Statistiky:</h4>";
            echo "<ul>";
            echo "<li><strong>Počet produktů:</strong> " . $data['statistics']['product_count'] . "</li>";
            echo "<li><strong>Počet uživatelů:</strong> " . $data['statistics']['user_count'] . "</li>";
            echo "</ul>";
            
            echo "<h4>Tabulky:</h4>";
            echo "<ul>";
            echo "<li><strong>Základní tabulky:</strong> " . $data['tables']['required_tables']['existing_count'] . "/" . $data['tables']['required_tables']['total'] . "</li>";
            echo "<li><strong>Inventurní tabulky:</strong> " . $data['tables']['inventory_tables']['existing_count'] . "/" . $data['tables']['inventory_tables']['total'] . "</li>";
            echo "</ul>";
            
            if (!empty($data['tables']['required_tables']['missing']) || !empty($data['tables']['inventory_tables']['missing'])) {
                $missingTables = array_merge($data['tables']['required_tables']['missing'], $data['tables']['inventory_tables']['missing']);
                echo "<div class='alert alert-warning' style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px;'>";
                echo "<strong>Chybějící tabulky:</strong> " . implode(', ', $missingTables);
                echo "</div>";
            }
            
        } else {
            echo "<span class='error'>✗ CHYBA</span> - API vrací chybu<br>";
            echo "<strong>Chyba:</strong> " . $data['error'] . "<br>";
        }
        
        echo "<h4>Úplná odpověď API:</h4>";
        echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        
    } else {
        echo "<span class='error'>✗ CHYBA</span> - API nevrací platný JSON<br>";
        echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
    }
} else {
    echo "<span class='error'>✗ CHYBA</span> - API neodpovídá<br>";
}
echo "</div>";

// Test 2: Simulace JavaScript volání
echo "<div class='test'>";
echo "<h3>Test 2: Simulace JavaScript načítání</h3>";
echo "<p>Klikněte na tlačítko pro testování načítání informací o databázi přes JavaScript:</p>";
echo "<button onclick='testDatabaseInfo()' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;'>Načíst informace o databázi</button>";
echo "<div id='db-info-test' style='margin-top: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; min-height: 50px;'>";
echo "<div class='text-center'>";
echo "<span>Klikněte na tlačítko pro načtení informací...</span>";
echo "</div>";
echo "</div>";

echo "<script>";
echo "async function testDatabaseInfo() {";
echo "    const dbInfoElement = document.getElementById('db-info-test');";
echo "    ";
echo "    // Zobrazíme loading";
echo "    dbInfoElement.innerHTML = '<div class=\"text-center\"><span style=\"color: blue;\">Načítání informací o databázi...</span></div>';";
echo "    ";
echo "    try {";
echo "        const response = await fetch('api/database_info.php');";
echo "        const data = await response.json();";
echo "        ";
echo "        console.log('Database info response:', data);";
echo "        ";
echo "        if (data.success) {";
echo "            const statusClass = data.connection_status === 'connected' ? 'color: green' : 'color: orange';";
echo "            const statusIcon = data.connection_status === 'connected' ? '✓' : '⚠';";
echo "            ";
echo "            dbInfoElement.innerHTML = `";
echo "                <div style=\"${statusClass}\">";
echo "                    <p style=\"margin-bottom: 5px;\"><strong>${statusIcon} \${data.status_message}</strong></p>";
echo "                </div>";
echo "                <div style=\"margin-top: 10px;\">";
echo "                    <ul style=\"list-style: none; padding: 0; margin: 0; font-size: 14px;\">";
echo "                        <li><strong>Server:</strong> \${data.database_info.host}</li>";
echo "                        <li><strong>Databáze:</strong> \${data.database_info.database}</li>";
echo "                        <li><strong>Uživatel:</strong> \${data.database_info.username}</li>";
echo "                        <li><strong>MySQL verze:</strong> \${data.database_info.mysql_version}</li>";
echo "                        <li><strong>Produkty:</strong> \${data.statistics.product_count}</li>";
echo "                        <li><strong>Uživatelé:</strong> \${data.statistics.user_count}</li>";
echo "                    </ul>";
echo "                </div>";
echo "                <div style=\"margin-top: 10px;\">";
echo "                    <small style=\"color: #666;\">";
echo "                        Základní tabulky: \${data.tables.required_tables.existing_count}/\${data.tables.required_tables.total} | ";
echo "                        Inventurní tabulky: \${data.tables.inventory_tables.existing_count}/\${data.tables.inventory_tables.total}";
echo "                    </small>";
echo "                </div>";
echo "                <div style=\"margin-top: 5px;\">";
echo "                    <small style=\"color: #666;\">Aktualizováno: \${data.timestamp}</small>";
echo "                </div>";
echo "            `;";
echo "        } else {";
echo "            dbInfoElement.innerHTML = `";
echo "                <div style=\"color: red;\">";
echo "                    <p style=\"margin-bottom: 5px;\"><strong>✗ \${data.status_message}</strong></p>";
echo "                </div>";
echo "                <div style=\"background: #f8d7da; border: 1px solid #f5c6cb; padding: 8px; border-radius: 3px; margin-top: 10px;\">";
echo "                    <small><strong>Chyba:</strong> \${data.error}</small>";
echo "                </div>";
echo "            `;";
echo "        }";
echo "    } catch (error) {";
echo "        console.error('Chyba při načítání informací o databázi:', error);";
echo "        dbInfoElement.innerHTML = `";
echo "            <div style=\"color: red;\">";
echo "                <p style=\"margin-bottom: 5px;\"><strong>✗ Chyba při načítání informací o databázi</strong></p>";
echo "            </div>";
echo "            <div style=\"background: #f8d7da; border: 1px solid #f5c6cb; padding: 8px; border-radius: 3px; margin-top: 10px;\">";
echo "                <small><strong>Chyba:</strong> \${error.message}</small>";
echo "            </div>";
echo "        `;";
echo "    }";
echo "}";
echo "</script>";
echo "</div>";

echo "<h2>📋 Doporučení</h2>";
echo "<ul>";
echo "<li>Pokud API funguje, informace o databázi se budou načítat automaticky v aplikaci</li>";
echo "<li>Pokud chybí tabulky, spusťte <a href='check_tables.php' target='_blank'>check_tables.php</a></li>";
echo "<li>Zkontrolujte konzoli prohlížeče (F12) pro případné chyby JavaScript</li>";
echo "</ul>";

echo "<p><a href='index.html' class='button'>Otevřít aplikaci</a> <a href='spustit_test.php' class='button'>Testovací dashboard</a></p>";

echo "</body>";
echo "</html>";
?>
