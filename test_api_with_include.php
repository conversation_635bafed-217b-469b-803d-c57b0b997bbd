<?php
/**
 * Test API pomocí include - simulace volání API
 */

// Spuštění session pouze pokud ještě není spuštěna
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>Test API pomocí include</h1>";

require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

try {
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✓ Připojení k databázi úspěšné</p>";
    
    echo "<h2>1. Test přihlášení</h2>";
    
    $user = authenticateUser('admin', 'admin123');
    
    if ($user) {
        echo "<p style='color: green;'>✓ Přihlášení úspěšné - uživatel: " . $user['username'] . " (ID: " . $user['id'] . ")</p>";
        
        // Uložíme uživatele do session pro API
        $_SESSION['user'] = $user;
        $_SESSION['test_mode'] = true; // Povolíme testovací režim
        
    } else {
        echo "<p style='color: red;'>✗ Přihlášení se nezdařilo</p>";
        exit;
    }
    
    echo "<h2>2. Test API produktů</h2>";
    
    // Simulace volání API produktů
    echo "<h3>Test vyhledání produktu přes API</h3>";
    
    // Nastavíme GET parametry pro API
    $_GET['action'] = 'search';
    $_GET['ean'] = '8594000000000';
    $_SERVER['REQUEST_METHOD'] = 'GET';
    
    // Zachytíme výstup API
    ob_start();
    
    try {
        // Simulujeme volání API
        include __DIR__ . '/api/products.php';
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Chyba při volání API produktů: " . $e->getMessage() . "</p>";
    }
    
    $apiOutput = ob_get_clean();
    
    echo "<p>Výstup API produktů:</p>";
    echo "<pre>" . htmlspecialchars($apiOutput) . "</pre>";
    
    // Zkusíme parsovat JSON odpověď
    $apiData = json_decode($apiOutput, true);
    
    if ($apiData) {
        if (isset($apiData['product'])) {
            echo "<p style='color: green;'>✓ API produktů funguje - produkt nalezen</p>";
            echo "<ul>";
            echo "<li>Název: " . ($apiData['product']['name'] ?? 'N/A') . "</li>";
            echo "<li>EAN: " . ($apiData['product']['ean_code'] ?? 'N/A') . "</li>";
            echo "</ul>";
        } elseif (isset($apiData['error'])) {
            echo "<p style='color: orange;'>⚠ API produktů vrátilo chybu: " . $apiData['error'] . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ API produktů nevrátilo platný JSON</p>";
    }
    
    echo "<h2>3. Test API inventury</h2>";
    
    // Simulace volání API inventury - získání relací
    echo "<h3>Test získání inventurních relací přes API</h3>";
    
    // Vyčistíme GET parametry
    unset($_GET['ean']);
    $_GET['action'] = 'sessions';
    $_SERVER['REQUEST_METHOD'] = 'GET';
    
    // Zachytíme výstup API
    ob_start();
    
    try {
        // Simulujeme volání API
        include __DIR__ . '/api/inventory.php';
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Chyba při volání API inventury: " . $e->getMessage() . "</p>";
    }
    
    $apiOutput = ob_get_clean();
    
    echo "<p>Výstup API inventury:</p>";
    echo "<pre>" . htmlspecialchars($apiOutput) . "</pre>";
    
    // Zkusíme parsovat JSON odpověď
    $apiData = json_decode($apiOutput, true);
    
    if ($apiData) {
        if (isset($apiData['sessions'])) {
            echo "<p style='color: green;'>✓ API inventury funguje - relace načteny: " . count($apiData['sessions']) . "</p>";
            
            if (count($apiData['sessions']) > 0) {
                $testSession = $apiData['sessions'][0];
                echo "<p>Testovací relace ID: " . $testSession['id'] . "</p>";
                
                // Test získání inventurních záznamů
                echo "<h3>Test získání inventurních záznamů přes API</h3>";
                
                $_GET['action'] = 'entries';
                $_GET['session_id'] = $testSession['id'];
                $_SERVER['REQUEST_METHOD'] = 'GET';
                
                // Zachytíme výstup API
                ob_start();
                
                try {
                    // Simulujeme volání API
                    include __DIR__ . '/api/inventory.php';
                } catch (Exception $e) {
                    echo "<p style='color: red;'>✗ Chyba při volání API inventury (záznamy): " . $e->getMessage() . "</p>";
                }
                
                $apiOutput = ob_get_clean();
                
                echo "<p>Výstup API inventury (záznamy):</p>";
                echo "<pre>" . htmlspecialchars($apiOutput) . "</pre>";
                
                // Zkusíme parsovat JSON odpověď
                $apiData = json_decode($apiOutput, true);
                
                if ($apiData) {
                    if (isset($apiData['entries'])) {
                        echo "<p style='color: green;'>✓ API inventury (záznamy) funguje - záznamy načteny: " . count($apiData['entries']) . "</p>";
                    } elseif (isset($apiData['error'])) {
                        echo "<p style='color: orange;'>⚠ API inventury (záznamy) vrátilo chybu: " . $apiData['error'] . "</p>";
                    }
                } else {
                    echo "<p style='color: red;'>✗ API inventury (záznamy) nevrátilo platný JSON</p>";
                }
            }
        } elseif (isset($apiData['error'])) {
            echo "<p style='color: orange;'>⚠ API inventury vrátilo chybu: " . $apiData['error'] . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ API inventury nevrátilo platný JSON</p>";
    }
    
    echo "<h2>4. Test vytvoření inventurní relace přes API</h2>";
    
    $_GET['action'] = 'sessions';
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    // Simulujeme POST data
    $postData = json_encode([]);
    file_put_contents('php://input', $postData);
    
    // Zachytíme výstup API
    ob_start();
    
    try {
        // Simulujeme volání API
        include __DIR__ . '/api/inventory.php';
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Chyba při vytváření inventurní relace přes API: " . $e->getMessage() . "</p>";
    }
    
    $apiOutput = ob_get_clean();
    
    echo "<p>Výstup API vytvoření relace:</p>";
    echo "<pre>" . htmlspecialchars($apiOutput) . "</pre>";
    
    // Zkusíme parsovat JSON odpověď
    $apiData = json_decode($apiOutput, true);
    
    if ($apiData) {
        if (isset($apiData['success']) && $apiData['success']) {
            echo "<p style='color: green;'>✓ API vytvoření relace funguje - relace vytvořena s ID: " . $apiData['session_id'] . "</p>";
        } elseif (isset($apiData['error'])) {
            echo "<p style='color: orange;'>⚠ API vytvoření relace vrátilo chybu: " . $apiData['error'] . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ API vytvoření relace nevrátilo platný JSON</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
}

echo "<h2>Závěr</h2>";
echo "<p><a href='index.html'>Zkusit hlavní aplikaci</a></p>";
echo "<p><a href='test_api_simple.php'>Test databáze přímo</a></p>";
?>
