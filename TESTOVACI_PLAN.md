# Testovací plán pro inventurní aplikaci

## Příprava na testování

### 1. Automatický test
- Spusťte `test_aplikace.php` v prohlížeči
- Zkontrolujte, zda všechny komponenty fungují správně
- Pokud jsou nějaké chyby, vy<PERSON>e<PERSON>te je před pokračováním

### 2. Otevření aplikace
- Otevřete `index.html` v prohlížeči
- URL: `http://localhost/PU/INVENTURA%20X/INVX1.5/index.html`

## Testovací scénáře

### Test 1: Přihlášení
**Cíl:** Ověřit funkčnost přihlašovacího systému

**Kroky:**
1. Na přihlašovací stránce zadejte:
   - Uživatelské jméno: `admin`
   - <PERSON><PERSON><PERSON>: `admin123`
2. Klikněte na "Přihlásit"

**Očekávaný výsledek:**
- Úspěšn<PERSON> přihlášení
- Zobrazení dashboardu
- V pravém horním rohu se zobrazí informace o přihlášeném uživateli

### Test 2: Vytvoření nové inventury
**Cíl:** Ověřit vytváření inventurních relací

**Kroky:**
1. Na dashboardu klikněte na "Nová inventura"
2. Zadejte název inventury (např. "Test inventura 1")
3. Potvrďte vytvoření

**Očekávaný výsledek:**
- Inventura se vytvoří a zobrazí v seznamu aktivních inventur
- Možnost vybrat inventuru pro práci

### Test 3: Vyhledávání produktů
**Cíl:** Ověřit funkčnost vyhledávání produktů podle EAN kódu

**Kroky:**
1. Přejděte na stránku "Inventura"
2. Vyberte aktivní inventuru (pokud není vybrána)
3. Do pole "EAN kód" zadejte existující EAN kód z databáze
4. Klikněte na "Vyhledat"

**Očekávaný výsledek:**
- Zobrazí se detaily produktu (název, cena, aktuální stav skladu)
- Formulář pro zadání množství se stane aktivním

### Test 4: Přidání produktu do inventury
**Cíl:** Ověřit přidávání produktů do inventury

**Kroky:**
1. Po úspěšném vyhledání produktu (Test 3)
2. Zadejte množství do pole "Zadané množství"
3. Klikněte na "Přidat do inventury"

**Očekávaný výsledek:**
- Produkt se přidá do tabulky inventurních záznamů
- Zobrazí se rozdíl mezi zadaným a aktuálním množstvím
- Formulář se vyčistí pro další zadávání

### Test 5: Správa inventurních záznamů
**Cíl:** Ověřit možnost úprav a mazání záznamů

**Kroky:**
1. V tabulce inventurních záznamů klikněte na ikonu koše u některého záznamu
2. Potvrďte smazání

**Očekávaný výsledek:**
- Záznam se smaže z tabulky
- Zobrazí se potvrzovací zpráva

### Test 6: Celková inventura (pro administrátory/manažery)
**Cíl:** Ověřit zobrazení celkové inventury

**Kroky:**
1. Přejděte na "Celková inventura" v menu
2. Vyberte inventuru ze seznamu

**Očekávaný výsledek:**
- Zobrazí se souhrnný přehled všech produktů ze všech uživatelů
- Správné sečtení množství od všech uživatelů

### Test 7: Správa uživatelů (pouze pro manažery)
**Cíl:** Ověřit funkčnost správy uživatelů

**Kroky:**
1. Přejděte na "Uživatelé" v menu
2. Klikněte na "Nový uživatel"
3. Vyplňte formulář a uložte
4. Zkuste upravit existujícího uživatele

**Očekávaný výsledek:**
- Nový uživatel se vytvoří
- Úpravy se uloží správně
- Seznam uživatelů se aktualizuje

### Test 8: Reporty a export
**Cíl:** Ověřit funkčnost exportu dat

**Kroky:**
1. Přejděte na "Reporty"
2. Vyberte inventuru pro export
3. Klikněte na "Exportovat do CSV"

**Očekávaný výsledek:**
- Stáhne se CSV soubor s daty inventury
- Data jsou správně formátována

### Test 9: Odhlášení
**Cíl:** Ověřit funkčnost odhlášení

**Kroky:**
1. Klikněte na "Odhlásit" v pravém horním rohu

**Očekávaný výsledek:**
- Uživatel se odhlásí
- Přesměrování na přihlašovací stránku

## Testování chybových stavů

### Test 10: Neplatné přihlašovací údaje
**Kroky:**
1. Zadejte neplatné uživatelské jméno nebo heslo
2. Pokuste se přihlásit

**Očekávaný výsledek:**
- Zobrazí se chybová zpráva
- Uživatel zůstane na přihlašovací stránce

### Test 11: Vyhledání neexistujícího produktu
**Kroky:**
1. Zadejte neexistující EAN kód
2. Klikněte na "Vyhledat"

**Očekávaný výsledek:**
- Zobrazí se zpráva "Produkt nebyl nalezen"
- Formulář pro zadání množství zůstane skrytý

### Test 12: Práce bez vybrané inventury
**Kroky:**
1. Pokuste se vyhledat produkt bez vybrané aktivní inventury

**Očekávaný výsledek:**
- Zobrazí se upozornění o nutnosti vybrat inventuru
- Akce se neprovede

## Poznámky pro testování

- **Testovací data:** Ujistěte se, že máte v databázi nějaké produkty s EAN kódy
- **Oprávnění:** Testujte s různými rolemi uživatelů (admin, manager, user)
- **Prohlížeče:** Otestujte v různých prohlížečích (Chrome, Firefox, Edge)
- **Responzivita:** Otestujte na různých velikostech obrazovky

## Hlášení chyb

Pokud najdete chybu, zaznamenejte:
1. **Kroky k reprodukci** - přesný postup, jak chybu vyvolat
2. **Očekávané chování** - co mělo fungovat
3. **Skutečné chování** - co se stalo místo toho
4. **Prostředí** - prohlížeč, verze, operační systém
5. **Chybové zprávy** - jakékoli chybové hlášky z konzole nebo aplikace

## Výsledek testování

Po dokončení všech testů vyhodnoťte:
- ✅ **Úspěšné testy:** Seznam testů, které prošly bez problémů
- ❌ **Neúspěšné testy:** Seznam testů s chybami
- ⚠️ **Poznámky:** Další pozorování nebo doporučení

---
*Testovací plán vytvořen: 2024*
