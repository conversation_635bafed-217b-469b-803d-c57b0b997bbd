<?php
/**
 * <PERSON><PERSON><PERSON><PERSON> test přihlášení
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='cs'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Rychlý test přihláš<PERSON>í</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".ok { color: green; font-weight: bold; }";
echo ".error { color: red; font-weight: bold; }";
echo ".warning { color: orange; font-weight: bold; }";
echo ".button { display: inline-block; padding: 10px 20px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; border: none; cursor: pointer; }";
echo ".result { margin: 15px 0; padding: 15px; border-left: 4px solid #ccc; background: #f9f9f9; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>⚡ Rychlý test přihlášení</h1>";

// Test 1: Kontrola admin uživatele
echo "<div class='result'>";
echo "<h3>1. Kontrola admin uživatele</h3>";
try {
    require_once 'utils/database.php';
    $pdo = getDbConnection();
    
    // Zajistíme, že tabulky existují
    ensureTablesExist();
    
    $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Admin uživatel existuje<br>";
        
        if ($admin['password'] === 'admin123') {
            echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Heslo je správné (admin123)<br>";
        } else {
            echo "<span class='warning'>⚠ OPRAVUJI</span> - Opravuji heslo...<br>";
            $stmt = $pdo->prepare("UPDATE inventory_users SET password = 'admin123' WHERE username = 'admin'");
            $stmt->execute();
            echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Heslo opraveno na admin123<br>";
        }
    } else {
        echo "<span class='warning'>⚠ VYTVÁŘÍM</span> - Vytvářím admin uživatele...<br>";
        $stmt = $pdo->prepare("
            INSERT INTO inventory_users (username, password, role, full_name, email, active) 
            VALUES ('admin', 'admin123', 'admin', 'Administrátor', '<EMAIL>', 1)
        ");
        $stmt->execute();
        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Admin uživatel vytvořen<br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>✗ CHYBA</span> - " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 2: Test API přihlášení
echo "<div class='result'>";
echo "<h3>2. Test API přihlášení</h3>";

$loginData = json_encode([
    'action' => 'login',
    'username' => 'admin',
    'password' => 'admin123'
]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $loginData,
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$loginResponse = @file_get_contents("http://localhost/PU/INVENTURA%20X/INVX1.5/api/simple_auth.php", false, $context);

if ($loginResponse !== false) {
    $loginJson = json_decode($loginResponse, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        if (isset($loginJson['success']) && $loginJson['success']) {
            echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - API přihlášení funguje<br>";
            echo "<strong>Uživatel:</strong> " . ($loginJson['user']['username'] ?? 'admin') . "<br>";
            echo "<strong>Role:</strong> " . ($loginJson['user']['role'] ?? 'admin') . "<br>";
        } else {
            echo "<span class='error'>✗ NEÚSPĚŠNÉ</span> - " . ($loginJson['error'] ?? 'Neznámá chyba') . "<br>";
        }
    } else {
        echo "<span class='error'>✗ CHYBA</span> - API nevrací platný JSON<br>";
    }
} else {
    echo "<span class='error'>✗ CHYBA</span> - API neodpovídá<br>";
}
echo "</div>";

// Test 3: JavaScript test
echo "<div class='result'>";
echo "<h3>3. Test přihlášení přes JavaScript</h3>";
echo "<button onclick='testLogin()' class='button'>🔐 Test přihlášení</button>";
echo "<div id='login-result' style='margin-top: 10px;'></div>";

echo "<script>";
echo "async function testLogin() {";
echo "    const resultDiv = document.getElementById('login-result');";
echo "    resultDiv.innerHTML = '<span style=\"color: blue;\">Testování přihlášení...</span>';";
echo "    ";
echo "    try {";
echo "        const response = await fetch('api/simple_auth.php', {";
echo "            method: 'POST',";
echo "            headers: { 'Content-Type': 'application/json' },";
echo "            body: JSON.stringify({ action: 'login', username: 'admin', password: 'admin123' })";
echo "        });";
echo "        ";
echo "        const data = await response.json();";
echo "        console.log('Login response:', data);";
echo "        ";
echo "        if (data.success) {";
echo "            resultDiv.innerHTML = '<span style=\"color: green; font-weight: bold;\">✓ ÚSPĚŠNÉ - Přihlášení funguje!</span><br>' +";
echo "                                  '<strong>Uživatel:</strong> ' + (data.user.username || 'admin') + '<br>' +";
echo "                                  '<strong>Role:</strong> ' + (data.user.role || 'admin');";
echo "        } else {";
echo "            resultDiv.innerHTML = '<span style=\"color: red; font-weight: bold;\">✗ NEÚSPĚŠNÉ - ' + (data.error || 'Neznámá chyba') + '</span>';";
echo "        }";
echo "    } catch (error) {";
echo "        console.error('Login error:', error);";
echo "        resultDiv.innerHTML = '<span style=\"color: red; font-weight: bold;\">✗ CHYBA - ' + error.message + '</span>';";
echo "    }";
echo "}";
echo "</script>";
echo "</div>";

echo "<h2>🎯 Výsledek</h2>";
echo "<div class='result'>";
echo "<p>Pokud všechny testy prošly, můžete se nyní přihlásit do aplikace:</p>";
echo "<ul>";
echo "<li><strong>Uživatelské jméno:</strong> admin</li>";
echo "<li><strong>Heslo:</strong> admin123</li>";
echo "</ul>";
echo "<p><a href='index.html' class='button' style='background: #28a745;'>🚀 Otevřít aplikaci a přihlásit se</a></p>";
echo "</div>";

echo "<h2>📋 Další nástroje</h2>";
echo "<p>";
echo "<a href='rychla_oprava_db.php' class='button'>🔧 Rychlá oprava DB</a>";
echo "<a href='debug_prihlaseni.php' class='button'>🔍 Debug přihlášení</a>";
echo "<a href='rychly_test.php' class='button'>⚡ Rychlý test</a>";
echo "</p>";

echo "</body>";
echo "</html>";
?>
