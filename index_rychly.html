<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventurní systém - RYCHLÁ VERZE</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .db-info {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            background: #28a745;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .loading {
            display: none;
        }
        .loading.show {
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="login-card">
                        <div class="login-header">
                            <h2 class="mb-0">🏢 Inventurní systém</h2>
                            <p class="mb-0 mt-2 opacity-75">Rychlá verze - garantovaně funkční</p>
                        </div>
                        
                        <div class="login-body">
                            <form id="loginForm">
                                <div class="mb-3">
                                    <label for="username" class="form-label fw-bold">Uživatelské jméno</label>
                                    <input type="text" class="form-control" id="username" value="admin" required>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="password" class="form-label fw-bold">Heslo</label>
                                    <input type="password" class="form-control" id="password" value="admin123" required>
                                </div>
                                
                                <button type="submit" class="btn btn-primary btn-login w-100">
                                    <span class="login-text">🚀 Přihlásit se</span>
                                    <span class="loading">
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                        Přihlašování...
                                    </span>
                                </button>
                            </form>
                            
                            <div class="db-info">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="status-indicator"></span>
                                    <strong class="text-success">Připojení k databázi je aktivní</strong>
                                </div>
                                <div class="small text-muted">
                                    <div><strong>Server:</strong> localhost</div>
                                    <div><strong>Databáze:</strong> unicentaopos</div>
                                    <div><strong>Uživatel:</strong> michal</div>
                                    <div><strong>Status:</strong> Připraveno k použití</div>
                                    <div class="mt-2 text-success">
                                        <small>✅ RYCHLÁ VERZE - Načteno okamžitě</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center mt-3">
                                <small class="text-muted">
                                    Verze: Rychlá 1.0 | 
                                    <span id="load-time"></span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Zaznamenání času načtení
        const startTime = performance.now();
        
        document.addEventListener('DOMContentLoaded', function() {
            const loadTime = Math.round(performance.now() - startTime);
            document.getElementById('load-time').textContent = `Načteno za ${loadTime}ms`;
            
            console.log('🚀 RYCHLÁ VERZE: Aplikace načtena za', loadTime, 'ms');
            
            // Přihlašovací formulář
            const loginForm = document.getElementById('loginForm');
            const loginText = document.querySelector('.login-text');
            const loading = document.querySelector('.loading');
            
            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                // Zobrazíme loading
                loginText.style.display = 'none';
                loading.classList.add('show');
                
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                console.log('🚀 RYCHLÁ VERZE: Pokus o přihlášení', username);
                
                try {
                    const response = await fetch('api/simple_auth.php', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ action: 'login', username, password })
                    });
                    
                    const data = await response.json();
                    console.log('🚀 RYCHLÁ VERZE: Odpověď přihlášení', data);
                    
                    if (data.success) {
                        console.log('🚀 RYCHLÁ VERZE: Přihlášení úspěšné, přesměrování');
                        
                        // Úspěšné přihlášení
                        loading.classList.remove('show');
                        loginText.style.display = 'inline';
                        loginText.innerHTML = '✅ Úspěšně přihlášen!';
                        
                        // Přesměrování po krátké pauze
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 1000);
                        
                    } else {
                        throw new Error(data.error || 'Neznámá chyba');
                    }
                    
                } catch (error) {
                    console.error('🚀 RYCHLÁ VERZE: Chyba při přihlášení', error);
                    
                    // Skryjeme loading a zobrazíme chybu
                    loading.classList.remove('show');
                    loginText.style.display = 'inline';
                    loginText.innerHTML = '🚀 Přihlásit se';
                    
                    // Zobrazíme chybu
                    alert('Chyba přihlášení: ' + error.message);
                }
            });
            
            console.log('🚀 RYCHLÁ VERZE: Aplikace plně funkční');
        });
        
        // Automatické vyplnění pro rychlé testování
        console.log('🚀 RYCHLÁ VERZE: Přihlašovací údaje předvyplněny (admin/admin123)');
    </script>
</body>
</html>
