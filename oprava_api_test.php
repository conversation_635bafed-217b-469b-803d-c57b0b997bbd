<?php
/**
 * Oprava API pro testování - dočasně vypne autentifikaci pro testovací <PERSON>
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='cs'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Oprava API pro testování</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".ok { color: green; font-weight: bold; }";
echo ".error { color: red; font-weight: bold; }";
echo ".warning { color: orange; font-weight: bold; }";
echo ".info { color: blue; font-weight: bold; }";
echo ".button { display: inline-block; padding: 10px 20px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }";
echo ".button:hover { background: #0056b3; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 Oprava API pro testování</h1>";

echo "<p>Tento skript dočasně upraví API soubory tak, aby umožnily testování bez nutnosti přihlášení.</p>";

$apiFiles = [
    'api/products.php',
    'api/inventory.php',
    'api/users.php'
];

$backupDir = 'backup_api_' . date('Y-m-d_H-i-s');

if (isset($_POST['action'])) {
    if ($_POST['action'] === 'backup_and_modify') {
        echo "<h2>📦 Vytváření zálohy a úprava API souborů</h2>";
        
        // Vytvoření záložního adresáře
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0777, true);
            echo "<p class='ok'>✓ Záložní adresář vytvořen: $backupDir</p>";
        }
        
        foreach ($apiFiles as $file) {
            if (file_exists($file)) {
                // Vytvoření zálohy
                $backupFile = $backupDir . '/' . basename($file);
                if (copy($file, $backupFile)) {
                    echo "<p class='ok'>✓ Záloha vytvořena: $backupFile</p>";
                    
                    // Úprava souboru
                    $content = file_get_contents($file);
                    
                    // Najdeme a zakomentujeme kontrolu přihlášení
                    $pattern = '/\/\/ Check if user is logged in.*?\n.*?exit;\s*\n.*?\}/s';
                    $replacement = '// Check if user is logged in (DISABLED FOR TESTING)
/*
if (!isLoggedIn() && !isset($_SESSION[\'test_mode\'])) {
    sendResponse([\'error\' => \'Unauthorized\'], 401);
    exit;
}
*/';
                    
                    $newContent = preg_replace($pattern, $replacement, $content);
                    
                    if ($newContent !== $content) {
                        if (file_put_contents($file, $newContent)) {
                            echo "<p class='ok'>✓ Soubor upraven: $file</p>";
                        } else {
                            echo "<p class='error'>✗ Chyba při úpravě souboru: $file</p>";
                        }
                    } else {
                        echo "<p class='warning'>⚠ Soubor nebylo třeba upravovat: $file</p>";
                    }
                } else {
                    echo "<p class='error'>✗ Chyba při vytváření zálohy: $file</p>";
                }
            } else {
                echo "<p class='warning'>⚠ Soubor neexistuje: $file</p>";
            }
        }
        
        echo "<h3>🎉 Úprava dokončena!</h3>";
        echo "<p>API soubory byly upraveny pro testování. Nyní můžete spustit testy.</p>";
        echo "<p><a href='rychly_test.php' class='button'>Spustit rychlý test</a></p>";
        
    } elseif ($_POST['action'] === 'restore') {
        echo "<h2>🔄 Obnovení původních API souborů</h2>";
        
        // Najdeme nejnovější záložní adresář
        $backupDirs = glob('backup_api_*', GLOB_ONLYDIR);
        if (!empty($backupDirs)) {
            rsort($backupDirs); // Seřadíme podle data (nejnovější první)
            $latestBackup = $backupDirs[0];
            
            echo "<p class='info'>Obnovuji ze zálohy: $latestBackup</p>";
            
            foreach ($apiFiles as $file) {
                $backupFile = $latestBackup . '/' . basename($file);
                if (file_exists($backupFile)) {
                    if (copy($backupFile, $file)) {
                        echo "<p class='ok'>✓ Soubor obnoven: $file</p>";
                    } else {
                        echo "<p class='error'>✗ Chyba při obnovení souboru: $file</p>";
                    }
                } else {
                    echo "<p class='warning'>⚠ Záložní soubor neexistuje: $backupFile</p>";
                }
            }
            
            echo "<h3>🎉 Obnovení dokončeno!</h3>";
            echo "<p>API soubory byly obnoveny do původního stavu.</p>";
        } else {
            echo "<p class='error'>✗ Žádné zálohy nenalezeny</p>";
        }
    }
} else {
    echo "<h2>⚙️ Dostupné akce</h2>";
    
    echo "<form method='post'>";
    echo "<p><button type='submit' name='action' value='backup_and_modify' class='button'>📦 Vytvořit zálohu a upravit API pro testování</button></p>";
    echo "<p><small>Tato akce vytvoří zálohu současných API souborů a upraví je tak, aby umožnily testování bez přihlášení.</small></p>";
    echo "</form>";
    
    echo "<form method='post'>";
    echo "<p><button type='submit' name='action' value='restore' class='button'>🔄 Obnovit původní API soubory</button></p>";
    echo "<p><small>Tato akce obnoví API soubory z nejnovější zálohy.</small></p>";
    echo "</form>";
    
    echo "<h2>📋 Současný stav</h2>";
    
    foreach ($apiFiles as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            $isModified = strpos($content, 'DISABLED FOR TESTING') !== false;
            
            echo "<p><strong>$file:</strong> ";
            if ($isModified) {
                echo "<span class='warning'>Upraven pro testování</span>";
            } else {
                echo "<span class='info'>Původní verze</span>";
            }
            echo "</p>";
        } else {
            echo "<p><strong>$file:</strong> <span class='error'>Neexistuje</span></p>";
        }
    }
    
    // Zobrazíme dostupné zálohy
    $backupDirs = glob('backup_api_*', GLOB_ONLYDIR);
    if (!empty($backupDirs)) {
        echo "<h3>📁 Dostupné zálohy</h3>";
        rsort($backupDirs);
        foreach ($backupDirs as $backup) {
            echo "<p>• $backup</p>";
        }
    }
}

echo "<hr>";
echo "<p><a href='spustit_test.php'>← Zpět na testovací dashboard</a></p>";

echo "</body>";
echo "</html>";
?>
