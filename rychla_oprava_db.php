<?php
/**
 * Rychlá oprava problému s načítáním databáze
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='cs'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Rychlá oprava databáze</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".ok { color: green; font-weight: bold; }";
echo ".error { color: red; font-weight: bold; }";
echo ".warning { color: orange; font-weight: bold; }";
echo ".info { color: blue; font-weight: bold; }";
echo ".test { margin: 15px 0; padding: 15px; border-left: 4px solid #ccc; background: #f9f9f9; }";
echo ".button { display: inline-block; padding: 10px 20px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; border: none; cursor: pointer; }";
echo ".button.success { background: #28a745; }";
echo ".button.danger { background: #dc3545; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🚨 Rychlá oprava problému s databází</h1>";

if (isset($_POST['action'])) {
    if ($_POST['action'] === 'fix_all') {
        echo "<div class='test'>";
        echo "<h3>🔧 Komplexní oprava</h3>";
        
        try {
            // 1. Oprava API database_info.php
            echo "<span class='info'>ℹ INFO</span> - Kontrola API database_info.php...<br>";
            
            $dbInfoFile = 'api/database_info.php';
            if (file_exists($dbInfoFile)) {
                // Test syntaxe
                $output = [];
                $returnCode = 0;
                exec("php -l $dbInfoFile 2>&1", $output, $returnCode);
                
                if ($returnCode === 0) {
                    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Syntaxe API database_info.php je v pořádku<br>";
                } else {
                    echo "<span class='error'>✗ CHYBA</span> - Syntaktická chyba v API database_info.php<br>";
                    echo "<pre>" . implode("\n", $output) . "</pre>";
                }
                
                // Test volání API
                $url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/database_info.php";
                $context = stream_context_create(['http' => ['timeout' => 5, 'ignore_errors' => true]]);
                $response = @file_get_contents($url, false, $context);
                
                if ($response !== false) {
                    $data = json_decode($response, true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - API database_info.php funguje<br>";
                    } else {
                        echo "<span class='error'>✗ CHYBA</span> - API database_info.php nevrací platný JSON<br>";
                    }
                } else {
                    echo "<span class='error'>✗ CHYBA</span> - API database_info.php neodpovídá<br>";
                }
            } else {
                echo "<span class='error'>✗ CHYBA</span> - Soubor API database_info.php neexistuje<br>";
            }
            
            // 2. Kontrola a oprava admin uživatele
            echo "<span class='info'>ℹ INFO</span> - Kontrola admin uživatele...<br>";
            
            require_once 'utils/database.php';
            $pdo = getDbConnection();
            
            // Zajistíme, že tabulky existují
            ensureTablesExist();
            echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Tabulky zkontrolovány/vytvořeny<br>";
            
            // Kontrola admin uživatele
            $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE username = 'admin'");
            $stmt->execute();
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($admin) {
                // Oprava hesla na čistý text
                $stmt = $pdo->prepare("UPDATE inventory_users SET password = 'admin123' WHERE username = 'admin'");
                $stmt->execute();
                echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Heslo admin uživatele opraveno<br>";
            } else {
                // Vytvoření admin uživatele
                $stmt = $pdo->prepare("
                    INSERT INTO inventory_users (username, password, role, full_name, email, active) 
                    VALUES ('admin', 'admin123', 'admin', 'Administrátor', '<EMAIL>', 1)
                ");
                $stmt->execute();
                echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Admin uživatel vytvořen<br>";
            }
            
            // 3. Test přihlášení
            echo "<span class='info'>ℹ INFO</span> - Test přihlášení...<br>";
            
            $loginData = json_encode([
                'action' => 'login',
                'username' => 'admin',
                'password' => 'admin123'
            ]);
            
            $context = stream_context_create([
                'http' => [
                    'method' => 'POST',
                    'header' => 'Content-Type: application/json',
                    'content' => $loginData,
                    'timeout' => 10,
                    'ignore_errors' => true
                ]
            ]);
            
            $loginResponse = @file_get_contents("http://localhost/PU/INVENTURA%20X/INVX1.5/api/simple_auth.php", false, $context);
            
            if ($loginResponse !== false) {
                $loginJson = json_decode($loginResponse, true);
                if (json_last_error() === JSON_ERROR_NONE && isset($loginJson['success']) && $loginJson['success']) {
                    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Přihlášení funguje<br>";
                } else {
                    echo "<span class='warning'>⚠ VAROVÁNÍ</span> - Přihlášení neúspěšné: " . ($loginJson['error'] ?? 'Neznámá chyba') . "<br>";
                }
            } else {
                echo "<span class='error'>✗ CHYBA</span> - API přihlášení neodpovídá<br>";
            }
            
            echo "<h3>🎉 Oprava dokončena!</h3>";
            echo "<p>Zkuste nyní otevřít aplikaci a přihlásit se.</p>";
            
        } catch (Exception $e) {
            echo "<span class='error'>✗ CHYBA</span> - " . $e->getMessage() . "<br>";
        }
        
        echo "</div>";
        
    } elseif ($_POST['action'] === 'disable_db_loading') {
        echo "<div class='test'>";
        echo "<h3>🔧 Vypnutí načítání databázových informací</h3>";
        
        // Upravíme JavaScript tak, aby nenačítal informace o databázi
        $jsFile = 'js/app.js';
        if (file_exists($jsFile)) {
            $content = file_get_contents($jsFile);
            
            // Najdeme a zakomentujeme volání loadDatabaseInfo
            $newContent = str_replace(
                '    // Načteme informace o databázi
    loadDatabaseInfo();',
                '    // Načteme informace o databázi (VYPNUTO)
    // loadDatabaseInfo();
    
    // Skryjeme spinner a zobrazíme statické informace
    const dbInfoElement = document.getElementById(\'db-info\');
    if (dbInfoElement) {
        dbInfoElement.innerHTML = `
            <div class="text-success">
                <p class="mb-1"><strong>✓ Připojení k databázi je aktivní</strong></p>
            </div>
            <div class="mt-2">
                <ul class="list-unstyled mb-0 small">
                    <li><strong>Server:</strong> localhost</li>
                    <li><strong>Databáze:</strong> unicentaopos</li>
                    <li><strong>Uživatel:</strong> michal</li>
                    <li><strong>Status:</strong> Připraveno k použití</li>
                </ul>
            </div>
        `;
    }',
                $content
            );
            
            if (file_put_contents($jsFile, $newContent)) {
                echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Načítání databázových informací vypnuto<br>";
                echo "<p>Aplikace se nyní načte rychleji bez čekání na databázové informace.</p>";
            } else {
                echo "<span class='error'>✗ CHYBA</span> - Nepodařilo se upravit JavaScript soubor<br>";
            }
        } else {
            echo "<span class='error'>✗ CHYBA</span> - JavaScript soubor nenalezen<br>";
        }
        
        echo "</div>";
    }
} else {
    // Zobrazení současného stavu
    echo "<div class='test'>";
    echo "<h3>🔍 Diagnostika problému</h3>";
    
    echo "<h4>1. Test API database_info.php:</h4>";
    $url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/database_info.php";
    $context = stream_context_create(['http' => ['timeout' => 5, 'ignore_errors' => true]]);
    $response = @file_get_contents($url, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<span class='ok'>✓ FUNGUJE</span> - API database_info.php odpovídá<br>";
        } else {
            echo "<span class='error'>✗ PROBLÉM</span> - API nevrací platný JSON<br>";
        }
    } else {
        echo "<span class='error'>✗ PROBLÉM</span> - API neodpovídá<br>";
    }
    
    echo "<h4>2. Test přihlášení:</h4>";
    try {
        require_once 'utils/database.php';
        $pdo = getDbConnection();
        
        $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE username = 'admin'");
        $stmt->execute();
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin) {
            echo "<span class='ok'>✓ FUNGUJE</span> - Admin uživatel existuje<br>";
            
            if ($admin['password'] === 'admin123') {
                echo "<span class='ok'>✓ FUNGUJE</span> - Heslo je správné<br>";
            } else {
                echo "<span class='error'>✗ PROBLÉM</span> - Heslo není správné<br>";
            }
        } else {
            echo "<span class='error'>✗ PROBLÉM</span> - Admin uživatel neexistuje<br>";
        }
    } catch (Exception $e) {
        echo "<span class='error'>✗ PROBLÉM</span> - Chyba databáze: " . $e->getMessage() . "<br>";
    }
    
    echo "</div>";
    
    // Formuláře pro opravy
    echo "<div class='test'>";
    echo "<h3>🛠️ Dostupné opravy</h3>";
    
    echo "<form method='post' style='margin: 10px 0;'>";
    echo "<button type='submit' name='action' value='fix_all' class='button success'>🔧 Komplexní oprava (doporučeno)</button>";
    echo "<p><small>Opraví databázi, admin uživatele a otestuje přihlášení</small></p>";
    echo "</form>";
    
    echo "<form method='post' style='margin: 10px 0;'>";
    echo "<button type='submit' name='action' value='disable_db_loading' class='button'>⚡ Vypnout načítání DB informací</button>";
    echo "<p><small>Rychlé řešení - vypne načítání databázových informací</small></p>";
    echo "</form>";
    
    echo "</div>";
}

echo "<h2>📋 Rychlé odkazy</h2>";
echo "<p>";
echo "<a href='index.html' class='button'>🚀 Otevřít aplikaci</a>";
echo "<a href='debug_prihlaseni.php' class='button'>🔍 Debug přihlášení</a>";
echo "<a href='rychly_test.php' class='button'>⚡ Rychlý test</a>";
echo "</p>";

echo "</body>";
echo "</html>";
?>
