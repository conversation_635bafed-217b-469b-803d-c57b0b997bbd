<?php
/**
 * Jednoduchý test API bez kontroly přihlášení
 */

// Spuštění session pouze pokud ještě není spuštěna
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>Jednoduchý test API</h1>";

require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

try {
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✓ Připojení k databázi úspěšné</p>";
    
    echo "<h2>1. Test přihlášení</h2>";
    
    $user = authenticateUser('admin', 'admin123');
    
    if ($user) {
        echo "<p style='color: green;'>✓ Přihlášení úspěšné - uživatel: " . $user['username'] . " (ID: " . $user['id'] . ")</p>";
        
        // Uložíme uživatele do session pro API
        $_SESSION['user'] = $user;
        
    } else {
        echo "<p style='color: red;'>✗ Přihlášení se nezdařilo</p>";
        exit;
    }
    
    echo "<h2>2. Test vyhledání produktu přímo</h2>";
    
    // Test vyhledání produktu přímo v databázi
    $testEan = '8594000000000'; // Testovací EAN
    
    echo "<h3>Test vyhledání produktu s EAN: $testEan</h3>";
    
    $stmt = $pdo->prepare("
        SELECT
            p.id,
            p.code AS ean_code,
            p.name,
            c.name AS category,
            p.pricebuy,
            t.rate AS tax_rate,
            p.pricesell,
            COALESCE(s.units, 0) AS current_stock
        FROM
            products p
        LEFT JOIN
            categories c ON p.category = c.id
        LEFT JOIN
            taxes t ON p.taxcat = t.id
        LEFT JOIN
            stockcurrent s ON p.id = s.product
        WHERE
            p.code = :ean_code
    ");
    
    $stmt->execute(['ean_code' => $testEan]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($product) {
        echo "<p style='color: green;'>✓ Produkt nalezen v databázi:</p>";
        echo "<ul>";
        echo "<li>ID: " . $product['id'] . "</li>";
        echo "<li>Název: " . $product['name'] . "</li>";
        echo "<li>EAN: " . $product['ean_code'] . "</li>";
        echo "<li>Aktuální stav: " . $product['current_stock'] . "</li>";
        echo "<li>Cena: " . $product['pricesell'] . "</li>";
        echo "</ul>";
        
        $testProduct = $product;
    } else {
        echo "<p style='color: orange;'>⚠ Produkt s EAN $testEan nebyl nalezen</p>";
        
        // Zkusíme najít jakýkoliv produkt
        echo "<h3>Hledání jakéhokoliv produktu</h3>";
        
        $stmt = $pdo->query("SELECT code, name, id FROM products LIMIT 1");
        $anyProduct = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($anyProduct) {
            $testEan = $anyProduct['code'];
            echo "<p>Zkusím s EAN: $testEan (ID: " . $anyProduct['id'] . ")</p>";
            
            $stmt = $pdo->prepare("
                SELECT
                    p.id,
                    p.code AS ean_code,
                    p.name,
                    c.name AS category,
                    p.pricebuy,
                    t.rate AS tax_rate,
                    p.pricesell,
                    COALESCE(s.units, 0) AS current_stock
                FROM
                    products p
                LEFT JOIN
                    categories c ON p.category = c.id
                LEFT JOIN
                    taxes t ON p.taxcat = t.id
                LEFT JOIN
                    stockcurrent s ON p.id = s.product
                WHERE
                    p.code = :ean_code
            ");
            
            $stmt->execute(['ean_code' => $testEan]);
            $product = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($product) {
                echo "<p style='color: green;'>✓ Produkt nalezen s jiným EAN:</p>";
                echo "<ul>";
                echo "<li>ID: " . $product['id'] . "</li>";
                echo "<li>Název: " . $product['name'] . "</li>";
                echo "<li>EAN: " . $product['ean_code'] . "</li>";
                echo "</ul>";
                $testProduct = $product;
            }
        }
    }
    
    echo "<h2>3. Test inventurních relací přímo</h2>";
    
    // Test získání inventurních relací přímo z databáze
    $stmt = $pdo->query("SELECT * FROM inventory_sessions ORDER BY created_at DESC LIMIT 5");
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($sessions) {
        echo "<p style='color: green;'>✓ Inventurní relace nalezeny: " . count($sessions) . " relací</p>";
        
        foreach ($sessions as $session) {
            echo "<p>Relace ID: " . $session['id'] . ", Status: " . $session['status'] . ", Vytvořeno: " . $session['created_at'] . "</p>";
        }
        
        $testSession = $sessions[0];
        
        echo "<h3>Test inventurních záznamů pro relaci ID: " . $testSession['id'] . "</h3>";
        
        $stmt = $pdo->prepare("
            SELECT
                ie.id,
                ie.product_id,
                ie.ean_code,
                p.name AS product_name,
                ie.zadane_mnozstvi,
                COALESCE(s.units, 0) AS current_stock,
                (ie.zadane_mnozstvi - COALESCE(s.units, 0)) AS difference,
                u.username,
                ie.last_updated
            FROM
                inventory_entries ie
            JOIN
                products p ON ie.product_id = p.id
            LEFT JOIN
                stockcurrent s ON p.id = s.product
            JOIN
                inventory_users u ON ie.user_id = u.id
            WHERE
                ie.session_id = :session_id
                AND ie.status = 'active'
            ORDER BY
                ie.last_updated DESC
            LIMIT 10
        ");
        
        $stmt->execute(['session_id' => $testSession['id']]);
        $entries = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($entries) {
            echo "<p style='color: green;'>✓ Inventurní záznamy nalezeny: " . count($entries) . " záznamů</p>";
            
            foreach ($entries as $entry) {
                echo "<p>Záznam ID: " . $entry['id'] . ", Produkt: " . $entry['product_name'] . ", EAN: " . $entry['ean_code'] . ", Množství: " . $entry['zadane_mnozstvi'] . "</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠ Žádné inventurní záznamy pro tuto relaci</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ Žádné inventurní relace</p>";
    }
    
    echo "<h2>4. Test vytvoření inventurní relace přímo</h2>";
    
    try {
        $stmt = $pdo->prepare("INSERT INTO inventory_sessions (user_id) VALUES (?)");
        $result = $stmt->execute([$user['id']]);
        
        if ($result) {
            $sessionId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✓ Inventurní relace vytvořena s ID: $sessionId</p>";
        } else {
            echo "<p style='color: red;'>✗ Chyba při vytváření inventurní relace</p>";
            echo "<p>Error info: " . print_r($stmt->errorInfo(), true) . "</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Chyba při vytváření inventurní relace: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
}

echo "<h2>Závěr</h2>";
echo "<p><a href='index.html'>Zkusit hlavní aplikaci</a></p>";
echo "<p><a href='test_api_with_include.php'>Test API s include</a></p>";
?>
