<?php
/**
 * Debug přihlašovacího procesu - krok za krokem
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='cs'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Debug přihlášení</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".ok { color: green; font-weight: bold; }";
echo ".error { color: red; font-weight: bold; }";
echo ".warning { color: orange; font-weight: bold; }";
echo ".info { color: blue; font-weight: bold; }";
echo ".test { margin: 15px 0; padding: 15px; border-left: 4px solid #ccc; background: #f9f9f9; }";
echo "pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }";
echo ".button { display: inline-block; padding: 10px 20px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔍 Debug přihlašovacího procesu</h1>";

// Krok 1: Kontrola konfigurace databáze
echo "<div class='test'>";
echo "<h3>Krok 1: Kontrola konfigurace databáze</h3>";
try {
    $config = require_once 'config/database.php';
    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Konfigurace načtena<br>";
    echo "<strong>Konfigurace:</strong><br>";
    echo "- Host: " . $config['host'] . "<br>";
    echo "- Database: " . $config['dbname'] . "<br>";
    echo "- Username: " . $config['username'] . "<br>";
    echo "- Charset: " . $config['charset'] . "<br>";
} catch (Exception $e) {
    echo "<span class='error'>✗ CHYBA</span> - " . $e->getMessage() . "<br>";
    exit;
}
echo "</div>";

// Krok 2: Test připojení k databázi
echo "<div class='test'>";
echo "<h3>Krok 2: Test připojení k databázi</h3>";
try {
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Připojení k databázi funguje<br>";
    
    // Test základního dotazu
    $stmt = $pdo->query("SELECT 1 as test");
    $result = $stmt->fetch();
    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Základní dotaz funguje<br>";
    
} catch (Exception $e) {
    echo "<span class='error'>✗ CHYBA</span> - " . $e->getMessage() . "<br>";
    exit;
}
echo "</div>";

// Krok 3: Kontrola tabulky inventory_users
echo "<div class='test'>";
echo "<h3>Krok 3: Kontrola tabulky inventory_users</h3>";
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_users'");
    if ($stmt->rowCount() > 0) {
        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Tabulka inventory_users existuje<br>";
        
        // Kontrola struktury tabulky
        $stmt = $pdo->query("DESCRIBE inventory_users");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<strong>Struktura tabulky:</strong><br>";
        foreach ($columns as $column) {
            echo "- {$column['Field']} ({$column['Type']})<br>";
        }
        
        // Kontrola admin uživatele
        $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE username = 'admin'");
        $stmt->execute();
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin) {
            echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Admin uživatel existuje<br>";
            echo "<strong>Admin údaje:</strong><br>";
            echo "- ID: " . $admin['id'] . "<br>";
            echo "- Username: " . $admin['username'] . "<br>";
            echo "- Role: " . $admin['role'] . "<br>";
            echo "- Active: " . ($admin['active'] ? 'Ano' : 'Ne') . "<br>";
            echo "- Password hash: " . substr($admin['password'], 0, 30) . "...<br>";
            
            // Test ověření hesla
            if (password_verify('admin123', $admin['password'])) {
                echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Heslo admin123 je správné<br>";
            } else {
                echo "<span class='error'>✗ CHYBA</span> - Heslo admin123 není správné<br>";
            }
        } else {
            echo "<span class='error'>✗ CHYBA</span> - Admin uživatel neexistuje<br>";
            
            // Pokusíme se vytvořit admin uživatele
            echo "<span class='info'>ℹ INFO</span> - Pokouším se vytvořit admin uživatele...<br>";
            try {
                $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("
                    INSERT INTO inventory_users (username, password, role, full_name, email, active, created_at) 
                    VALUES ('admin', ?, 'admin', 'Administrátor', '<EMAIL>', 1, NOW())
                ");
                $stmt->execute([$hashedPassword]);
                echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Admin uživatel vytvořen<br>";
            } catch (Exception $e) {
                echo "<span class='error'>✗ CHYBA</span> - Nelze vytvořit admin uživatele: " . $e->getMessage() . "<br>";
            }
        }
        
    } else {
        echo "<span class='error'>✗ CHYBA</span> - Tabulka inventory_users neexistuje<br>";
        echo "<span class='info'>ℹ INFO</span> - Spusťte check_tables.php pro vytvoření tabulek<br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>✗ CHYBA</span> - " . $e->getMessage() . "<br>";
}
echo "</div>";

// Krok 4: Test API simple_auth.php
echo "<div class='test'>";
echo "<h3>Krok 4: Test API simple_auth.php</h3>";

// Test check akce
echo "<h4>4a) Test check akce (GET):</h4>";
$url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/simple_auth.php?action=check";
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$response = @file_get_contents($url, false, $context);
if ($response !== false) {
    $data = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - API check funguje<br>";
        echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<span class='error'>✗ CHYBA</span> - API nevrací platný JSON<br>";
        echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
    }
} else {
    echo "<span class='error'>✗ CHYBA</span> - API neodpovídá<br>";
}

// Test login akce
echo "<h4>4b) Test login akce (POST):</h4>";
$loginData = json_encode([
    'action' => 'login',
    'username' => 'admin',
    'password' => 'admin123'
]);

$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => $loginData,
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$loginResponse = @file_get_contents("http://localhost/PU/INVENTURA%20X/INVX1.5/api/simple_auth.php", false, $context);
if ($loginResponse !== false) {
    $loginJson = json_decode($loginResponse, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        if (isset($loginJson['success']) && $loginJson['success']) {
            echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - API login funguje správně<br>";
        } else {
            echo "<span class='error'>✗ CHYBA</span> - API login neúspěšné<br>";
        }
        echo "<pre>" . json_encode($loginJson, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<span class='error'>✗ CHYBA</span> - API login nevrací platný JSON<br>";
        echo "<pre>" . htmlspecialchars(substr($loginResponse, 0, 500)) . "</pre>";
    }
} else {
    echo "<span class='error'>✗ CHYBA</span> - API login neodpovídá<br>";
}
echo "</div>";

// Krok 5: Kontrola session
echo "<div class='test'>";
echo "<h3>Krok 5: Kontrola session</h3>";
session_start();
echo "<strong>Session ID:</strong> " . session_id() . "<br>";
echo "<strong>Session save path:</strong> " . session_save_path() . "<br>";
echo "<strong>Session status:</strong> " . session_status() . " (1=disabled, 2=active)<br>";
echo "<strong>Session obsah:</strong><br>";
echo "<pre>" . print_r($_SESSION, true) . "</pre>";
echo "</div>";

// Krok 6: Test přímého přihlášení
echo "<div class='test'>";
echo "<h3>Krok 6: Test přímého přihlášení</h3>";
echo "<p>Klikněte na tlačítko pro test přihlášení přímo z této stránky:</p>";
echo "<button onclick='testDirectLogin()' style='padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;'>Test přihlášení</button>";
echo "<div id='login-test-result' style='margin-top: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; min-height: 50px;'>";
echo "<span>Klikněte na tlačítko pro test...</span>";
echo "</div>";

echo "<script>";
echo "async function testDirectLogin() {";
echo "    const resultDiv = document.getElementById('login-test-result');";
echo "    resultDiv.innerHTML = '<span style=\"color: blue;\">Testování přihlášení...</span>';";
echo "    ";
echo "    try {";
echo "        const response = await fetch('api/simple_auth.php', {";
echo "            method: 'POST',";
echo "            headers: {";
echo "                'Content-Type': 'application/json'";
echo "            },";
echo "            body: JSON.stringify({";
echo "                action: 'login',";
echo "                username: 'admin',";
echo "                password: 'admin123'";
echo "            })";
echo "        });";
echo "        ";
echo "        const data = await response.json();";
echo "        console.log('Login test response:', data);";
echo "        ";
echo "        if (data.success) {";
echo "            resultDiv.innerHTML = '<span style=\"color: green; font-weight: bold;\">✓ ÚSPĚŠNÉ - Přihlášení funguje!</span><br><pre>' + JSON.stringify(data, null, 2) + '</pre>';";
echo "        } else {";
echo "            resultDiv.innerHTML = '<span style=\"color: red; font-weight: bold;\">✗ NEÚSPĚŠNÉ - ' + (data.error || 'Neznámá chyba') + '</span><br><pre>' + JSON.stringify(data, null, 2) + '</pre>';";
echo "        }";
echo "    } catch (error) {";
echo "        console.error('Login test error:', error);";
echo "        resultDiv.innerHTML = '<span style=\"color: red; font-weight: bold;\">✗ CHYBA - ' + error.message + '</span>';";
echo "    }";
echo "}";
echo "</script>";
echo "</div>";

echo "<h2>📋 Souhrn a doporučení</h2>";
echo "<ul>";
echo "<li>Pokud všechny kroky prošly, problém může být v JavaScript kódu aplikace</li>";
echo "<li>Zkontrolujte konzoli prohlížeče (F12) při pokusu o přihlášení v aplikaci</li>";
echo "<li>Porovnejte chování tohoto testu s aplikací</li>";
echo "<li>Pokud admin uživatel neexistoval, byl vytvořen automaticky</li>";
echo "</ul>";

echo "<p><a href='index.html' class='button'>Zkusit přihlášení v aplikaci</a> <a href='spustit_test.php' class='button'>Testovací dashboard</a></p>";

echo "</body>";
echo "</html>";
?>
