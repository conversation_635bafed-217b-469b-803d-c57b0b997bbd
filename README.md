# Inventurní systém

Webová aplikace pro správu inventur s napojením na UniCentaOPOS databázi.

## 🚀 Rych<PERSON> testování

### Automatické testy
1. **Rychlý test:** Otevřete `rychly_test.php` v prohlížeči
2. **Kompletní test:** Otevřete `test_aplikace.php` v prohlížeči
3. **Testovací dashboard:** Otevřete `spustit_test.php` v prohlížeči

### Manuální testování
1. Otevřete `index.html` v prohlížeči
2. Přihlaste se s údaji: **admin** / **admin123**
3. Postupujte podle `TESTOVACI_PLAN.md`

## 📋 Funkce aplikace

- ✅ Přihlašování uživatelů s různými rolemi (admin, manager, user)
- ✅ Vytváření a správa inventurních relací
- ✅ Vyhledávání produktů podle EAN kódu
- ✅ Zadávání inventurních množství
- ✅ Automatická synchronizace s prodejními daty
- ✅ Celková inventura (součet od všech uživatelů)
- ✅ Export dat do CSV
- ✅ Správa uživatelů (pro manažery)
- ✅ Responzivní design pro různá zařízení

## 🔧 Požadavky

- PHP 7.4 nebo vyšší
- MySQL/MariaDB
- Webový server (Apache, Nginx)
- Moderní webový prohlížeč
- UniCentaOPOS databáze

## 🛠️ Instalace a nastavení

1. Zkopírujte soubory do webového adresáře
2. Upravte konfiguraci databáze v `config/database.php`
3. Spusťte `check_tables.php` pro vytvoření potřebných tabulek
4. Přihlaste se s výchozími údaji: admin / admin123

## 📋 Testovací scénáře

### Základní testy
- [x] Přihlášení a odhlášení
- [x] Vytvoření nové inventury
- [x] Vyhledávání produktů podle EAN
- [x] Přidávání produktů do inventury
- [x] Mazání inventurních záznamů
- [x] Zobrazení celkové inventury

### Pokročilé testy
- [x] Správa uživatelů (vytvoření, úprava, mazání)
- [x] Export dat do CSV
- [x] Synchronizace s prodejními daty
- [x] Oprávnění podle rolí uživatelů

## 📁 Struktura souborů

```
├── index.html              # Hlavní aplikace
├── rychly_test.php         # Rychlý test funkčnosti
├── test_aplikace.php       # Kompletní test
├── spustit_test.php        # Testovací dashboard
├── TESTOVACI_PLAN.md       # Detailní testovací plán
├── config/
│   └── database.php        # Konfigurace databáze
├── api/                    # API endpointy
├── js/                     # JavaScript soubory
├── css/                    # Styly
└── utils/                  # Pomocné PHP třídy
```

## 🐛 Řešení problémů

### Aplikace se nenačítá
1. Zkontrolujte, zda webový server běží
2. Ověřte cestu k aplikaci v prohlížeči
3. Spusťte `rychly_test.php` pro diagnostiku

### Chyby databáze
1. Zkontrolujte připojení v `config/database.php`
2. Ověřte, že UniCentaOPOS databáze existuje
3. Spusťte `check_tables.php` pro vytvoření chybějících tabulek

### API nefunguje
1. Zkontrolujte, zda jsou soubory v adresáři `api/` dostupné
2. Ověřte oprávnění souborů
3. Zkontrolujte error log webového serveru

## 📞 Podpora

Pro technickou podporu nebo hlášení chyb kontaktujte vývojáře aplikace.

## 📝 Poznámky k verzi

Tato verze aplikace obsahuje všechny požadované funkce:
- Kompletní inventurní systém
- Synchronizace s UniCentaOPOS
- Správa uživatelů a oprávnění
- Automatické testování
- Responzivní design

---
*Inventurní systém v1.5 - 2024*
