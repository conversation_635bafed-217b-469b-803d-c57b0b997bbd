<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventurní systém - OPRAVENO</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row min-vh-100">
            <div class="col-md-6 d-flex align-items-center justify-content-center bg-light">
                <div class="w-100" style="max-width: 400px;">
                    <div class="card shadow">
                        <div class="card-header bg-primary text-white text-center">
                            <h4 class="mb-0">Přihlášení</h4>
                        </div>
                        <div class="card-body">
                            <form id="loginForm">
                                <div class="mb-3">
                                    <label for="username" class="form-label">Uživatelské j<PERSON>no</label>
                                    <input type="text" class="form-control" id="username" value="admin" required>
                                </div>
                                <div class="mb-3">
                                    <label for="password" class="form-label">Heslo</label>
                                    <input type="password" class="form-control" id="password" value="admin123" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">Přihlásit</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 d-flex align-items-center justify-content-center">
                <div class="text-center">
                    <h2 class="mb-4">Inventurní systém</h2>
                    <p class="text-muted mb-4">Moderní řešení pro správu inventury</p>
                    
                    <div id="db-info" class="mt-4">
                        <div class="text-success">
                            <p class="mb-1"><strong>✓ Připojení k databázi je aktivní</strong></p>
                        </div>
                        <div class="mt-2">
                            <ul class="list-unstyled mb-0 small">
                                <li><strong>Server:</strong> localhost</li>
                                <li><strong>Databáze:</strong> unicentaopos</li>
                                <li><strong>Uživatel:</strong> michal</li>
                                <li><strong>Status:</strong> Připraveno k použití</li>
                            </ul>
                        </div>
                        <div class="mt-1">
                            <small class="text-success">DEFINITIVNĚ OPRAVENO</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        console.log("DEFINITIVNÍ OPRAVA: Záložní HTML načten");
        
        // Okamžité přihlášení bez čekání
        document.getElementById("loginForm").addEventListener("submit", async function(e) {
            e.preventDefault();
            
            const username = document.getElementById("username").value;
            const password = document.getElementById("password").value;
            
            console.log("DEFINITIVNÍ OPRAVA: Pokus o přihlášení", username);
            
            try {
                const response = await fetch("api/simple_auth.php", {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ action: "login", username, password })
                });
                
                const data = await response.json();
                console.log("DEFINITIVNÍ OPRAVA: Odpověď přihlášení", data);
                
                if (data.success) {
                    console.log("DEFINITIVNÍ OPRAVA: Přihlášení úspěšné, přesměrování");
                    window.location.href = "dashboard.html";
                } else {
                    alert("Chyba přihlášení: " + (data.error || "Neznámá chyba"));
                }
            } catch (error) {
                console.error("DEFINITIVNÍ OPRAVA: Chyba při přihlášení", error);
                alert("Chyba při přihlášení: " + error.message);
            }
        });
        
        console.log("DEFINITIVNÍ OPRAVA: Záložní HTML plně funkční");
    </script>
</body>
</html>