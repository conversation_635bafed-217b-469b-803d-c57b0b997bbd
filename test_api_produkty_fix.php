<?php
/**
 * Test API produkty po opravě syntaktické chyby
 */

header('Content-Type: text/html; charset=utf-8');

echo "<!DOCTYPE html>";
echo "<html lang='cs'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Test API produkty - oprava</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".ok { color: green; font-weight: bold; }";
echo ".error { color: red; font-weight: bold; }";
echo ".warning { color: orange; font-weight: bold; }";
echo ".info { color: blue; font-weight: bold; }";
echo ".test { margin: 15px 0; padding: 15px; border-left: 4px solid #ccc; background: #f9f9f9; }";
echo "pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }";
echo ".button { display: inline-block; padding: 10px 20px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; border: none; cursor: pointer; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 Test API produkty po opravě</h1>";

// Test 1: Kontrola syntaxe PHP souboru
echo "<div class='test'>";
echo "<h3>Test 1: Kontrola syntaxe API souboru</h3>";

$apiFile = 'api/products.php';
if (file_exists($apiFile)) {
    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Soubor $apiFile existuje<br>";
    
    // Test syntaxe pomocí php -l
    $output = [];
    $returnCode = 0;
    exec("php -l $apiFile 2>&1", $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Syntaxe PHP je v pořádku<br>";
    } else {
        echo "<span class='error'>✗ CHYBA</span> - Syntaktická chyba v PHP:<br>";
        echo "<pre>" . implode("\n", $output) . "</pre>";
    }
} else {
    echo "<span class='error'>✗ CHYBA</span> - Soubor $apiFile neexistuje<br>";
}
echo "</div>";

// Test 2: Kontrola databáze a produktů
echo "<div class='test'>";
echo "<h3>Test 2: Kontrola databáze a produktů</h3>";
try {
    $config = require_once 'config/database.php';
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    
    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Připojení k databázi<br>";
    
    // Kontrola tabulky products
    $stmt = $pdo->query("SHOW TABLES LIKE 'products'");
    if ($stmt->rowCount() > 0) {
        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Tabulka products existuje<br>";
        
        // Počet produktů
        $stmt = $pdo->query("SELECT COUNT(*) FROM products");
        $count = $stmt->fetchColumn();
        echo "<strong>Počet produktů:</strong> $count<br>";
        
        if ($count > 0) {
            // Najdeme produkty s EAN kódem
            $stmt = $pdo->query("SELECT code, name FROM products WHERE code IS NOT NULL AND code != '' LIMIT 5");
            $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($products)) {
                echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Nalezeny produkty s EAN kódy<br>";
                echo "<strong>Ukázkové produkty:</strong><br>";
                foreach ($products as $product) {
                    echo "- {$product['code']}: {$product['name']}<br>";
                }
                
                // Použijeme první produkt pro test
                $testEan = $products[0]['code'];
                echo "<strong>Testovací EAN:</strong> $testEan<br>";
            } else {
                echo "<span class='warning'>⚠ VAROVÁNÍ</span> - Žádné produkty s EAN kódy<br>";
                $testEan = 'test'; // Použijeme testovací EAN
            }
        } else {
            echo "<span class='warning'>⚠ VAROVÁNÍ</span> - Žádné produkty v databázi<br>";
            $testEan = 'test'; // Použijeme testovací EAN
        }
    } else {
        echo "<span class='error'>✗ CHYBA</span> - Tabulka products neexistuje<br>";
        $testEan = 'test';
    }
} catch (Exception $e) {
    echo "<span class='error'>✗ CHYBA</span> - " . $e->getMessage() . "<br>";
    $testEan = 'test';
}
echo "</div>";

// Test 3: Test API volání
echo "<div class='test'>";
echo "<h3>Test 3: Test API volání</h3>";

$url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/products.php?action=search&ean=" . urlencode($testEan);
echo "<strong>URL:</strong> $url<br>";

$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 10,
        'ignore_errors' => true
    ]
]);

$response = @file_get_contents($url, false, $context);

if ($response !== false) {
    echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - API odpovídá<br>";
    
    // Zkontrolujeme HTTP status
    $httpStatus = $http_response_header[0] ?? 'Neznámý status';
    echo "<strong>HTTP Status:</strong> $httpStatus<br>";
    
    // Zkontrolujeme Content-Type
    $contentType = '';
    foreach ($http_response_header as $header) {
        if (stripos($header, 'content-type:') === 0) {
            $contentType = trim(substr($header, 13));
            break;
        }
    }
    echo "<strong>Content-Type:</strong> $contentType<br>";
    
    // Zkontrolujeme JSON
    $data = json_decode($response, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - API vrací platný JSON<br>";
        
        if (isset($data['product'])) {
            echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Produkt nalezen<br>";
        } elseif (isset($data['error'])) {
            if ($data['error'] === 'Product not found') {
                echo "<span class='info'>ℹ INFO</span> - Produkt nenalezen (očekávané pro testovací EAN)<br>";
            } else {
                echo "<span class='warning'>⚠ VAROVÁNÍ</span> - API vrací chybu: " . $data['error'] . "<br>";
            }
        }
        
        echo "<strong>Odpověď API:</strong><br>";
        echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    } else {
        echo "<span class='error'>✗ CHYBA</span> - API nevrací platný JSON<br>";
        echo "<strong>JSON chyba:</strong> " . json_last_error_msg() . "<br>";
        echo "<strong>Surová odpověď:</strong><br>";
        echo "<pre>" . htmlspecialchars(substr($response, 0, 1000)) . "</pre>";
    }
} else {
    echo "<span class='error'>✗ CHYBA</span> - API neodpovídá<br>";
}
echo "</div>";

// Test 4: Test s existujícím produktem (pokud máme)
if (isset($products) && !empty($products)) {
    echo "<div class='test'>";
    echo "<h3>Test 4: Test s existujícím produktem</h3>";
    
    $realEan = $products[0]['code'];
    $url = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/products.php?action=search&ean=" . urlencode($realEan);
    echo "<strong>URL:</strong> $url<br>";
    echo "<strong>EAN:</strong> $realEan<br>";
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            if (isset($data['product'])) {
                echo "<span class='ok'>✓ ÚSPĚŠNÉ</span> - Existující produkt nalezen<br>";
                echo "<strong>Název produktu:</strong> " . $data['product']['name'] . "<br>";
                echo "<strong>EAN:</strong> " . $data['product']['ean_code'] . "<br>";
                echo "<strong>Cena:</strong> " . $data['product']['pricesell'] . "<br>";
                echo "<strong>Aktuální stav:</strong> " . $data['product']['current_stock'] . "<br>";
            } else {
                echo "<span class='warning'>⚠ VAROVÁNÍ</span> - Existující produkt nenalezen<br>";
                echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
            }
        } else {
            echo "<span class='error'>✗ CHYBA</span> - Neplatný JSON pro existující produkt<br>";
        }
    } else {
        echo "<span class='error'>✗ CHYBA</span> - API neodpovídá pro existující produkt<br>";
    }
    echo "</div>";
}

// Test 5: JavaScript test
echo "<div class='test'>";
echo "<h3>Test 5: JavaScript test API</h3>";
echo "<button onclick='testApiWithJs()' class='button'>Test API přes JavaScript</button>";
echo "<div id='js-test-result' style='margin-top: 10px;'></div>";

echo "<script>";
echo "async function testApiWithJs() {";
echo "    const resultDiv = document.getElementById('js-test-result');";
echo "    resultDiv.innerHTML = '<span style=\"color: blue;\">Testování API přes JavaScript...</span>';";
echo "    ";
echo "    try {";
echo "        const testEan = '" . ($testEan ?? 'test') . "';";
echo "        const url = 'api/products.php?action=search&ean=' + encodeURIComponent(testEan);";
echo "        ";
echo "        const response = await fetch(url);";
echo "        const data = await response.json();";
echo "        ";
echo "        console.log('API response:', data);";
echo "        ";
echo "        if (data.product) {";
echo "            resultDiv.innerHTML = '<span style=\"color: green; font-weight: bold;\">✓ ÚSPĚŠNÉ - Produkt nalezen</span><br><pre>' + JSON.stringify(data, null, 2) + '</pre>';";
echo "        } else if (data.error) {";
echo "            if (data.error === 'Product not found') {";
echo "                resultDiv.innerHTML = '<span style=\"color: blue; font-weight: bold;\">ℹ INFO - Produkt nenalezen (očekávané)</span><br><pre>' + JSON.stringify(data, null, 2) + '</pre>';";
echo "            } else {";
echo "                resultDiv.innerHTML = '<span style=\"color: orange; font-weight: bold;\">⚠ VAROVÁNÍ - ' + data.error + '</span><br><pre>' + JSON.stringify(data, null, 2) + '</pre>';";
echo "            }";
echo "        } else {";
echo "            resultDiv.innerHTML = '<span style=\"color: red; font-weight: bold;\">✗ NEOČEKÁVANÁ ODPOVĚĎ</span><br><pre>' + JSON.stringify(data, null, 2) + '</pre>';";
echo "        }";
echo "    } catch (error) {";
echo "        console.error('API test error:', error);";
echo "        resultDiv.innerHTML = '<span style=\"color: red; font-weight: bold;\">✗ CHYBA - ' + error.message + '</span>';";
echo "    }";
echo "}";
echo "</script>";
echo "</div>";

echo "<h2>📋 Souhrn</h2>";
echo "<ul>";
echo "<li><strong>Opravena syntaktická chyba</strong> v api/products.php (navíc */ na řádku 31)</li>";
echo "<li><strong>API by nyní mělo fungovat</strong> správně</li>";
echo "<li><strong>Testovací EAN:</strong> " . ($testEan ?? 'test') . "</li>";
echo "</ul>";

echo "<p><a href='rychly_test.php' class='button'>Spustit rychlý test</a> <a href='index.html' class='button'>Otevřít aplikaci</a></p>";

echo "</body>";
echo "</html>";
?>
