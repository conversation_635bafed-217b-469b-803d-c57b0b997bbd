/**
 * Inventurní systém - Hlavní JavaScript soubor
 */

// Globální proměnné
let currentUser = null;
let activeSession = null;
let activeSessionEntries = [];
let isAdmin = false;
let isAdminOrManager = false;

// Konfigurace API
const API_URL = 'api';
const PROXY_URL = null; // Proxy není potřeba pro lokální API

// Pomocná funkce pro vytvoření URL s parametry
function buildApiUrl(endpoint, params = {}) {
    console.log('buildApiUrl - endpoint:', endpoint);
    console.log('buildApiUrl - params:', params);

    // Upravíme endpoint pro API
    // Speciální případ pro simple_auth
    if (endpoint === 'simple_auth') {
        endpoint = 'simple_auth.php';
    } else {
        // Pokud endpoint obsahuje lomítko (např. 'auth/login'), použijeme první část jako soubor a druhou jako parametr action
        if (endpoint.includes('/')) {
            const parts = endpoint.split('/');
            const file = parts[0];
            const action = parts[1];

            // Pokud poslední část je číslo (ID), použijeme PATH_INFO
            if (!isNaN(action)) {
                endpoint = file + '.php/' + action;
            } else {
                // Jinak použijeme parametr action
                endpoint = endpoint + '.php';
            }
        } else {
            // Pokud endpoint neobsahuje lomítko, jednoduše přidáme .php';
            endpoint = endpoint + '.php';
        }
    }

    console.log('buildApiUrl - upravený endpoint:', endpoint);

    // Upravíme cestu k API tak, aby neobsahovala dvojité lomítko
    let baseUrl = API_URL;
    if (baseUrl.endsWith('/')) {
        baseUrl = baseUrl.slice(0, -1);
    }

    let url = baseUrl + '/' + endpoint;
    console.log('buildApiUrl - základní URL:', url);

    // Přidání parametrů do URL
    const queryParams = new URLSearchParams();

    for (const key in params) {
        if (params[key] !== null && params[key] !== undefined) {
            queryParams.append(key, params[key]);
        }
    }

    const queryString = queryParams.toString();
    if (queryString) {
        url += `?${queryString}`;
    }

    console.log('buildApiUrl - výsledná URL:', url);
    // Proxy není potřeba pro lokální API
    return url;
}

/**
 * Kontrola a vytvoření potřebných tabulek a výchozího administrátorského účtu
 *
 * @returns {Promise} Promise, který se vyřeší po dokončení kontroly
 */
function checkTables() {
    return fetch('check_tables.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se zkontrolovat tabulky');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                console.log('Tabulky a výchozí administrátorský účet byly úspěšně zkontrolovány');
            } else {
                console.error('Chyba při kontrole tabulek:', data.error);
            }
        });
}

/**
 * Prozatímní funkce pro přepnutí sekce změny hesla.
 */
function togglePasswordChangeSection() {
    console.warn('Funkce togglePasswordChangeSection je volána, ale není implementována.');
    // Zde může být například alert:
    // alert('Funkce pro změnu hesla zatím není implementována.');
}

/**
 * Zpracování vyhledávání produktů
 *
 * @param {Event} e Událost
 */
function handleProductSearch(e) {
    e.preventDefault();

    // Kontrola, zda je vybrána aktivní relace
    if (!activeSession) {
        showNotification('Nejprve vyberte nebo vytvořte inventurní relaci', 'error');
        return;
    }

    const eanCode = document.getElementById('ean-code').value;

    fetch(`api/products.php?action=search&ean=${eanCode}`)
        .then(response => response.json())
        .then(data => {
            if (data.product) {
                // Zobrazení detailů produktu
                document.getElementById('product-name').textContent = data.product.name;
                document.getElementById('product-ean').textContent = data.product.ean_code;
                document.getElementById('product-category').textContent = data.product.category || '-';
                // Zobrazení nákupní ceny bez DPH
                document.getElementById('product-pricebuy').textContent = formatPrice(data.product.pricebuy);
                document.getElementById('product-price').textContent = formatPrice(data.product.pricesell);
                // Zobrazení DPH
                const taxRate = parseFloat(data.product.tax_rate) || 0;
                document.getElementById('product-tax').textContent = (taxRate * 100).toFixed(0);

                // Zobrazení ceny s DPH
                const priceWithTax = parseFloat(data.product.price_with_tax) || 0;
                document.getElementById('product-price-with-tax').textContent = formatPrice(priceWithTax);

                document.getElementById('product-stock').textContent = formatQuantity(data.product.current_stock);

                // Zobrazení formuláře pro zadání množství
                document.getElementById('product-details').classList.remove('d-none');
                document.getElementById('zadane-mnozstvi').value = data.product.current_stock;
                document.getElementById('zadane-mnozstvi').focus();

                // Skrytí chybové zprávy
                document.getElementById('product-search-error').classList.add('d-none');
            } else {
                // Zobrazení chybové zprávy
                document.getElementById('product-details').classList.add('d-none');
                document.getElementById('product-search-error').textContent = data.error || 'Produkt nebyl nalezen';
                document.getElementById('product-search-error').classList.remove('d-none');
            }
        })
        .catch(error => {
            console.error('Chyba při vyhledávání produktu:', error);
            document.getElementById('product-details').classList.add('d-none');
            document.getElementById('product-search-error').textContent = 'Došlo k chybě při vyhledávání produktu';
            document.getElementById('product-search-error').classList.remove('d-none');
        });
}

/**
 * Zpracování přidání produktu do inventury
 *
 * @param {Event} e Událost
 */
function handleAddInventoryEntry(e) {
    e.preventDefault();

    // Kontrola, zda je vybrána aktivní relace
    if (!activeSession) {
        showNotification('Nejprve vyberte nebo vytvořte inventurní relaci', 'error');
        return;
    }

    const eanCode = document.getElementById('product-ean').textContent;
    const zadaneMnozstvi = parseFloat(document.getElementById('zadane-mnozstvi').value);

    fetch(`api/inventory.php?action=entries`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            session_id: activeSession.id,
            ean_code: eanCode,
            zadane_mnozstvi: zadaneMnozstvi
        })
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Vyčištění formuláře
                document.getElementById('ean-code').value = '';
                document.getElementById('product-details').classList.add('d-none');

                // Načtení aktualizovaných záznamů
                loadInventoryEntries();

                showNotification('Produkt byl úspěšně přidán do inventury', 'success');
            } else {
                showNotification(data.error || 'Došlo k chybě při přidávání produktu do inventury', 'error');
            }
        })
        .catch(error => {
            console.error('Chyba při přidávání produktu do inventury:', error);
            showNotification('Došlo k chybě při přidávání produktu do inventury', 'error');
        });
}

/**
 * Načtení inventurních záznamů pro aktivní relaci
 */
function loadInventoryEntries() {
    if (!activeSession) {
        console.log('loadInventoryEntries - žádná aktivní relace');
        return;
    }

    console.log('loadInventoryEntries - načítání záznamů pro relaci:', activeSession.id);

    fetch(`api/inventory.php?action=entries&session_id=${activeSession.id}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('loadInventoryEntries - data:', data);

            if (data.success && data.entries) {
                displayInventoryEntries(data.entries);
            } else {
                console.error('loadInventoryEntries - chyba:', data.error);
                showNotification(data.error || 'Došlo k chybě při načítání inventurních záznamů', 'error');
            }
        })
        .catch(error => {
            console.error('loadInventoryEntries - chyba:', error);
            showNotification('Došlo k chybě při načítání inventurních záznamů', 'error');
        });
}

/**
 * Zobrazení inventurních záznamů v tabulce
 */
function displayInventoryEntries(entries) {
    const inventoryTableBody = document.querySelector('#inventory-table tbody');
    if (!inventoryTableBody) {
        console.error('displayInventoryEntries - tabulka inventury nenalezena');
        return;
    }

    if (!entries || entries.length === 0) {
        inventoryTableBody.innerHTML = '<tr><td colspan="6" class="text-center">Žádné záznamy</td></tr>';
        return;
    }

    inventoryTableBody.innerHTML = entries.map(entry => `
        <tr data-entry-id="${entry.id}">
            <td>${entry.product_name || 'N/A'}</td>
            <td>${entry.ean_code}</td>
            <td class="text-end">${formatQuantity(entry.zadane_mnozstvi)}</td>
            <td class="text-end">${formatQuantity(entry.current_stock)}</td>
            <td class="text-end">${formatQuantity(entry.rozdil)}</td>
            <td>
                <button class="btn btn-sm btn-danger" onclick="deleteEntry(${entry.id})">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        </tr>
    `).join(\'\');
}

/**
 * Nastavení vyhledávání produktů
 */
function setupProductSearch() {
    const productSearchForm = document.getElementById('product-search-form');
    if (productSearchForm) {
        productSearchForm.addEventListener('submit', handleProductSearch);
    }

    const inventoryEntryForm = document.getElementById('inventory-entry-form');
    if (inventoryEntryForm) {
        inventoryEntryForm.addEventListener('submit', handleAddInventoryEntry);
    }
}

/**
 * Smazání inventurního záznamu
 *
 * @param {number} entryId ID záznamu
 */
function deleteEntry(entryId) {
    if (!confirm('Opravdu chcete smazat tento záznam?')) {
        return;
    }

    console.log('Mazání záznamu s ID:', entryId);

    fetch(`api/inventory.php?action=entry`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ id: entryId, _method: 'DELETE' })
    })
        .then(response => {
            if (!response.ok) {
                console.error('Chyba při mazání záznamu, HTTP status:', response.status);
                return response.json().then(data => {
                    throw new Error(data.error || `HTTP chyba: ${response.status}`);
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                console.log('Záznam byl úspěšně smazán:', data);
                showNotification('Záznam byl úspěšně smazán', 'success');
                loadInventoryEntries();
            } else {
                console.error('Chyba při mazání záznamu:', data.error);
                showNotification(data.error || 'Došlo k chybě při mazání záznamu', 'error');
            }
        })
        .catch(error => {
            console.error('Chyba při mazání záznamu:', error);
            showNotification('Došlo k chybě při mazání záznamu: ' + error.message, 'error');
        });
}

/**
 * Formátování ceny
 */
function formatPrice(price) {
    if (price === null || price === undefined || price === '') {
        return '0,00 Kč';
    }

    const numPrice = parseFloat(price);
    if (isNaN(numPrice)) {
        return '0,00 Kč';
    }

    return numPrice.toLocaleString('cs-CZ', {
        style: 'currency',
        currency: 'CZK',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

/**
 * Formátování množství
 */
function formatQuantity(quantity) {
    if (quantity === null || quantity === undefined || quantity === '') {
        return '0';
    }

    const numQuantity = parseFloat(quantity);
    if (isNaN(numQuantity)) {
        return '0';
    }

    // Pokud je to celé číslo, zobrazíme bez desetinných míst
    if (numQuantity % 1 === 0) {
        return numQuantity.toString();
    }

    // Jinak zobrazíme s až 3 desetinnými místy
    return numQuantity.toLocaleString('cs-CZ', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 3
    });
}

/**
 * Zobrazení notifikace
 */
function showNotification(message, type = 'info') {
    // Vytvoříme notifikační element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Přidáme do stránky
    document.body.appendChild(notification);

    // Automatické odstranění po 5 sekundách
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// Inicializace aplikace
document.addEventListener('DOMContentLoaded', () => {
    console.log('Inicializace aplikace...');

    // DEFINITIVNÍ OPRAVA - Okamžité zobrazení bez čekání
    console.log('DEFINITIVNÍ OPRAVA: Spouštím okamžité zobrazení databázových informací');
    
    // Najdeme element pro databázové informace
    const dbInfoElement = document.getElementById('db-info');
    if (dbInfoElement) {
        console.log('DEFINITIVNÍ OPRAVA: Element #db-info nalezen, nastavujem obsah');
        
        // Okamžitě nastavíme obsah bez jakéhokoli čekání
        dbInfoElement.innerHTML = `
            <div class="text-success">
                <p class="mb-1"><strong>✓ Připojení k databázi je aktivní</strong></p>
            </div>
            <div class="mt-2">
                <ul class="list-unstyled mb-0 small">
                    <li><strong>Server:</strong> localhost</li>
                    <li><strong>Databáze:</strong> unicentaopos</li>
                    <li><strong>Uživatel:</strong> michal</li>
                    <li><strong>Status:</strong> Připraveno k použití</li>
                </ul>
            </div>
            <div class="mt-1">
                <small class="text-success">DEFINITIVNĚ OPRAVENO - ${new Date().toLocaleString('cs-CZ')}</small>
            </div>
        `;
        
        console.log('DEFINITIVNÍ OPRAVA: Obsah nastaven, databázové informace zobrazeny');
    } else {
        console.error('DEFINITIVNÍ OPRAVA: Element #db-info nenalezen!');
    }
    
    // Ujistíme se, že se žádné načítání nespustí
    if (typeof loadDatabaseInfo === 'function') {
        console.log('DEFINITIVNÍ OPRAVA: Funkce loadDatabaseInfo zakázána');
        window.loadDatabaseInfo = function() {
            console.log('DEFINITIVNÍ OPRAVA: loadDatabaseInfo byla zakázána');
            return false;
        };
    }